import { APPEARANCE, BORDER_TOP_ARROW_POSITION, ROUNDED, TEXT_SIZE_WEIGHT } from "src/components/commonComponentStyles";
import { SingleSelectDropdownInterface } from "./SingleSelectDropdown.types";
import { styled } from "styled-components";

const StyledDiv = styled.div`
  border: 1px solid;
  float: left;
  padding: 8px;
  position: absolute;
  margin: 0;
  max-height: 256px;
  z-index: 100;
`;

const StyledArrowDiv = styled.div`
  border: 1px solid;
  float: left;
  padding: 8px;
  position: absolute;
  margin: 0;
  max-height: 256px;
  top: 0.5em;
  z-index: 100;
  &:before {
    content: "";
    position: absolute;
    top: -15px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 16px 16px 16px;
    z-index: 99;
  }
  &:after {
    content: "";
    position: absolute;
    top: -16px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 16px 16px 16px;
    z-index: 98;
  }
`;

const SingleSelectDropdown = ({
  appearance = "input",
  classNameContainer = "",
  classNameInput = "",
  borderArrowPosition = "default",
  isContentVisible,
  isDisabled = false,
  items,
  labelKey,
  name,
  handleInputFocus,
  handleInputBlur,
  handleItemSelection,
  placeholder = "",
  rounded = "none",
  selectedItem = undefined,
  showInput = true,
  textSize = "sm",
  useArrowBorder = false,
}: SingleSelectDropdownInterface) => {
  return (
    <>
      {showInput && (
        <input
          readOnly
          type="text"
          name={name}
          disabled={isDisabled}
          value={selectedItem?.label}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          className={`w-full ${classNameInput}`}
          placeholder={placeholder}
        />
      )}
      {isContentVisible && useArrowBorder && (
        <StyledArrowDiv
          className={`
              bg-bdo-bg-04-white
              shadow-md cursor-pointer
              before:border-l-transparent
              before:border-r-transparent
              before:border-t-transparent
              before:border-b-bdo-bg-04-white
              after:border-l-transparent
              after:border-r-transparent
              after:border-t-transparent
              after:border-b-bdo-bg-01-charcoal
              after:shadow-t-lg
              after:shadow-t-bdo-bg-01-charcoal
              ${ROUNDED[rounded]}
              ${BORDER_TOP_ARROW_POSITION[borderArrowPosition]}
              ${classNameContainer}
            `}
          onMouseLeave={handleInputBlur}
        >
          {labelKey &&
            items &&
            items.length > 0 &&
            items.map((item, index) => {
              return (
                <div
                  className={`
                      ${index < items.length - 1 ? "pb-2" : ""}
                      ${APPEARANCE[appearance]}
                      ${TEXT_SIZE_WEIGHT[textSize]}
                      ${
                        selectedItem && selectedItem?.value === item.value
                          ? "bg-bdo-bg-02-charcoal-pale text-bdo-primary-white"
                          : ""
                      }
                    `}
                  key={index}
                  onMouseDown={() => handleItemSelection(item)}
                >
                  {item[labelKey]}
                </div>
              );
            })}
        </StyledArrowDiv>
      )}
      {isContentVisible && !useArrowBorder && (
        <StyledDiv
          className={`
              bg-bdo-bg-04-white
              shadow-md cursor-pointer
              ${ROUNDED[rounded]}
              ${classNameContainer}
            `}
          onMouseLeave={handleInputBlur}
        >
          {labelKey &&
            items &&
            items.length > 0 &&
            items.map((item, index) => {
              return (
                <div
                  className={`
                      ${index < items.length - 1 ? "pb-2" : ""}
                      ${APPEARANCE[appearance]}
                      ${TEXT_SIZE_WEIGHT[textSize]}
                      ${
                        selectedItem && selectedItem?.value === item.value
                          ? "bg-bdo-bg-02-charcoal-pale text-bdo-primary-white"
                          : ""
                      }
                    `}
                  key={index}
                  onMouseDown={() => handleItemSelection(item)}
                >
                  {item[labelKey]}
                </div>
              );
            })}
        </StyledDiv>
      )}
    </>
  );
};

export default SingleSelectDropdown;
