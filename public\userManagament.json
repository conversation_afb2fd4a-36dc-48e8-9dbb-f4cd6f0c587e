[{"userObjectId": "b1a7e1e2-1c2d-4f3a-8e4b-1a2b3c4d5e6f", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-10T09:00:00Z", "dateUpdated": "2024-05-01T12:00:00Z", "isSystemAdmin": false, "reportCount": 550, "jobTitle": "Customer Service Representative"}, {"userObjectId": "c2b8f2e3-2d3e-5f4b-9f5c-2b3c4d5e6f7a", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-11-15T10:30:00Z", "dateUpdated": "2024-04-20T15:00:00Z", "isSystemAdmin": true, "reportCount": 750, "jobTitle": "Call Center Manager"}, {"userObjectId": "d3c9g3f4-3e4f-6g5c-0g6d-3c4d5e6f7a8b", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-20T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 300, "jobTitle": "Customer Service Representative"}, {"userObjectId": "e4d0h4g5-4f5g-7h6d-1h7e-4d5e6f7a8b9c", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-03-05T11:15:00Z", "dateUpdated": "2024-05-10T13:00:00Z", "isSystemAdmin": false, "reportCount": 200, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f5e1i5h6-5g6h-8i7e-2i8f-5e6f7a8b9c0d", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-12-01T14:00:00Z", "dateUpdated": "2024-05-15T16:00:00Z", "isSystemAdmin": true, "reportCount": 900, "jobTitle": "Operations Supervisor"}, {"userObjectId": "a6f2j6i7-6h7i-9j8f-3j9g-6f7a8b9c0d1e", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Edmonton", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-20T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 400, "jobTitle": "Customer Service Representative"}, {"userObjectId": "b7g3k7j8-7i8j-0k9g-4k0h-7a8b9c0d1e2f", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Halifax", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-10-10T13:45:00Z", "dateUpdated": "2024-03-25T11:00:00Z", "isSystemAdmin": true, "reportCount": 600, "jobTitle": "Team Lead"}, {"userObjectId": "c8h4l8k9-8j9k-1l0h-5l1i-8b9c0d1e2f3g", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Winnipeg", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-05T10:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 350, "jobTitle": "Customer Service Representative"}, {"userObjectId": "d9i5m9l0-9k0l-2m1i-6m2j-9c0d1e2f3g4h", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Quebec City", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-15T15:30:00Z", "dateUpdated": "2024-04-05T14:00:00Z", "isSystemAdmin": true, "reportCount": 800, "jobTitle": "Call Center Manager"}, {"userObjectId": "e0j6n0m1-0l1m-3n2j-7n3k-0d1e2f3g4h5i", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-25T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 550, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "IT", "isDeleted": false, "dateAdded": "2024-01-10T09:00:00Z", "dateUpdated": "2024-05-01T12:00:00Z", "isSystemAdmin": false, "reportCount": 120, "jobTitle": "IT Support"}, {"userObjectId": "f1000001-0000-0000-0000-000000000001", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-16T09:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 132, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-12-11T10:30:00Z", "dateUpdated": "2024-04-26T15:00:00Z", "isSystemAdmin": false, "reportCount": 92, "jobTitle": "Accountant"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "IT", "isDeleted": false, "dateAdded": "2024-02-26T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 48, "jobTitle": "IT Support"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "Ava Bell", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-11-21T11:15:00Z", "dateUpdated": "2024-05-13T13:00:00Z", "isSystemAdmin": true, "reportCount": 325, "jobTitle": "Team Lead"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "HR", "isDeleted": false, "dateAdded": "2023-12-16T14:00:00Z", "dateUpdated": "2024-05-19T16:00:00Z", "isSystemAdmin": false, "reportCount": 69, "jobTitle": "HR Specialist"}, {"userObjectId": "f1000001-0000-0000-0000-000000000006", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Edmonton", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-23T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 415, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-000000000007", "email": "<EMAIL>", "fullName": "Oliver <PERSON>", "location": "Halifax", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-10-13T13:45:00Z", "dateUpdated": "2024-03-28T11:00:00Z", "isSystemAdmin": true, "reportCount": 615, "jobTitle": "Team Lead"}, {"userObjectId": "f1000001-0000-0000-0000-000000000008", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Winnipeg", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-08T10:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 362, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-000000000009", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Quebec City", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-18T15:30:00Z", "dateUpdated": "2024-04-08T14:00:00Z", "isSystemAdmin": true, "reportCount": 815, "jobTitle": "Call Center Manager"}, {"userObjectId": "f1000001-0000-0000-0000-000000000010", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-28T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 565, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-000000000011", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-29T09:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 134, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-12-12T10:30:00Z", "dateUpdated": "2024-04-27T15:00:00Z", "isSystemAdmin": false, "reportCount": 93, "jobTitle": "Accountant"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "IT", "isDeleted": false, "dateAdded": "2024-02-27T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 49, "jobTitle": "IT Support"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-11-22T11:15:00Z", "dateUpdated": "2024-05-14T13:00:00Z", "isSystemAdmin": true, "reportCount": 326, "jobTitle": "Team Lead"}, {"userObjectId": "f1000001-0000-0000-0000-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "HR", "isDeleted": false, "dateAdded": "2023-12-17T14:00:00Z", "dateUpdated": "2024-05-20T16:00:00Z", "isSystemAdmin": false, "reportCount": 70, "jobTitle": "HR Specialist"}, {"userObjectId": "f1000001-0000-0000-0000-000000000016", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Edmonton", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-24T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 416, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-000000000017", "email": "<EMAIL>", "fullName": "Mia Hall", "location": "Halifax", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-10-14T13:45:00Z", "dateUpdated": "2024-03-29T11:00:00Z", "isSystemAdmin": true, "reportCount": 616, "jobTitle": "Team Lead"}, {"userObjectId": "f1000001-0000-0000-0000-000000000018", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Winnipeg", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-09T10:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 363, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f1000001-0000-0000-0000-000000000019", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Quebec City", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-19T15:30:00Z", "dateUpdated": "2024-04-09T14:00:00Z", "isSystemAdmin": true, "reportCount": 816, "jobTitle": "Call Center Manager"}, {"userObjectId": "f1000001-0000-0000-0000-000000000020", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-29T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 566, "jobTitle": "Customer Service Representative"}, {"userObjectId": "a1111111-1111-1111-1111-111111111111", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-15T09:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 123, "jobTitle": "Customer Service Representative"}, {"userObjectId": "b2222222-2222-2222-2222-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-12-10T10:30:00Z", "dateUpdated": "2024-04-25T15:00:00Z", "isSystemAdmin": false, "reportCount": 87, "jobTitle": "Accountant"}, {"userObjectId": "c3333333-3333-3333-3333-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "IT", "isDeleted": false, "dateAdded": "2024-02-25T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 45, "jobTitle": "IT Support"}, {"userObjectId": "d4444444-4444-4444-4444-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-11-20T11:15:00Z", "dateUpdated": "2024-05-12T13:00:00Z", "isSystemAdmin": true, "reportCount": 321, "jobTitle": "Team Lead"}, {"userObjectId": "e5555555-5555-5555-5555-************", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "HR", "isDeleted": false, "dateAdded": "2023-12-15T14:00:00Z", "dateUpdated": "2024-05-18T16:00:00Z", "isSystemAdmin": false, "reportCount": 67, "jobTitle": "HR Specialist"}, {"userObjectId": "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "IT", "isDeleted": false, "dateAdded": "2024-01-12T09:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 110, "jobTitle": "IT Support"}, {"userObjectId": "2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-11-18T10:30:00Z", "dateUpdated": "2024-04-22T15:00:00Z", "isSystemAdmin": false, "reportCount": 90, "jobTitle": "Accountant"}, {"userObjectId": "3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "HR", "isDeleted": false, "dateAdded": "2024-02-22T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 70, "jobTitle": "HR Specialist"}, {"userObjectId": "4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9g", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-03-07T11:15:00Z", "dateUpdated": "2024-05-12T13:00:00Z", "isSystemAdmin": false, "reportCount": 210, "jobTitle": "Customer Service Representative"}, {"userObjectId": "5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9g0h", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-12-03T14:00:00Z", "dateUpdated": "2024-05-17T16:00:00Z", "isSystemAdmin": true, "reportCount": 910, "jobTitle": "Operations Supervisor"}, {"userObjectId": "6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9g0h1i", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Edmonton", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-22T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 410, "jobTitle": "Customer Service Representative"}, {"userObjectId": "7a8b9c0d-1e2f-3a4b-5c6d-7e8f9g0h1i2j", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Halifax", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-10-12T13:45:00Z", "dateUpdated": "2024-03-27T11:00:00Z", "isSystemAdmin": true, "reportCount": 610, "jobTitle": "Team Lead"}, {"userObjectId": "8b9c0d1e-2f3a-4b5c-6d7e-8f9g0h1i2j3k", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Winnipeg", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-07T10:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 360, "jobTitle": "Customer Service Representative"}, {"userObjectId": "9c0d1e2f-3a4b-5c6d-7e8f-9g0h1i2j3k4l", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Quebec City", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-17T15:30:00Z", "dateUpdated": "2024-04-07T14:00:00Z", "isSystemAdmin": true, "reportCount": 810, "jobTitle": "Call Center Manager"}, {"userObjectId": "0d1e2f3a-4b5c-6d7e-8f9g-0h1i2j3k4l5m", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-27T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 560, "jobTitle": "Customer Service Representative"}, {"userObjectId": "a2b3c4d5-e6f7-8a9b-0c1d-2e3f4a5b6c7d", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-11-15T10:30:00Z", "dateUpdated": "2024-04-20T15:00:00Z", "isSystemAdmin": false, "reportCount": 80, "jobTitle": "Accountant"}, {"userObjectId": "b3c4d5e6-f7a8-9b0c-1d2e-3f4a5b6c7d8e", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "HR", "isDeleted": false, "dateAdded": "2024-02-20T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 60, "jobTitle": "HR Specialist"}, {"userObjectId": "c4d5e6f7-a8b9-0c1d-2e3f-4a5b6c7d8e9f", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-03-05T11:15:00Z", "dateUpdated": "2024-05-10T13:00:00Z", "isSystemAdmin": false, "reportCount": 200, "jobTitle": "Customer Service Representative"}, {"userObjectId": "d5e6f7a8-b9c0-1d2e-3f4a-5b6c7d8e9f0a", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-12-01T14:00:00Z", "dateUpdated": "2024-05-15T16:00:00Z", "isSystemAdmin": true, "reportCount": 900, "jobTitle": "Operations Supervisor"}, {"userObjectId": "e6f7a8b9-c0d1-2e3f-4a5b-6c7d8e9f0a1b", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Edmonton", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-20T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 400, "jobTitle": "Customer Service Representative"}, {"userObjectId": "f7a8b9c0-d1e2-3f4a-5b6c-7d8e9f0a1b2c", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Halifax", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-10-10T13:45:00Z", "dateUpdated": "2024-03-25T11:00:00Z", "isSystemAdmin": true, "reportCount": 600, "jobTitle": "Team Lead"}, {"userObjectId": "a8b9c0d1-e2f3-4a5b-6c7d-8e9f0a1b2c3d", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Winnipeg", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-05T10:00:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 350, "jobTitle": "Customer Service Representative"}, {"userObjectId": "b9c0d1e2-f3a4-5b6c-7d8e-9f0a1b2c3d4e", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Quebec City", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-15T15:30:00Z", "dateUpdated": "2024-04-05T14:00:00Z", "isSystemAdmin": true, "reportCount": 800, "jobTitle": "Call Center Manager"}, {"userObjectId": "c0d1e2f3-a4b5-6c7d-8e9f-0a1b2c3d4e5f", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-25T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 550, "jobTitle": "Customer Service Representative"}, {"userObjectId": "d1e2f3a4-b5c6-7d8e-9f0a-1b2c3d4e5f6g", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-25T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 550, "jobTitle": "Customer Service Representative"}, {"userObjectId": "e2f3a4b5-c6d7-8e9f-0a1b-2c3d4e5f6g7h", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-12-01T14:00:00Z", "dateUpdated": "2024-05-15T16:00:00Z", "isSystemAdmin": true, "reportCount": 700, "jobTitle": "Finance Manager"}, {"userObjectId": "f3a4b5c6-d7e8-9f0a-1b2c-3d4e5f6g7h8i", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-20T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 250, "jobTitle": "Customer Service Representative"}, {"userObjectId": "g4b5c6d7-e8f9-0a1b-2c3d-4e5f6g7h8i9j", "email": "hannah.hernan<PERSON>@bdo.ca", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-15T15:30:00Z", "dateUpdated": "2024-04-05T14:00:00Z", "isSystemAdmin": true, "reportCount": 950, "jobTitle": "Call Center Manager"}, {"userObjectId": "h5c6d7e8-f9g0-1b2c-3d4e-5f6g7h8i9j0k", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Toronto", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-25T08:15:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 600, "jobTitle": "Customer Service Representative"}, {"userObjectId": "i6d7e8f9-g0h1-2c3d-4e5f-6g7h8i9j0k1l", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-11-15T10:30:00Z", "dateUpdated": "2024-04-20T15:00:00Z", "isSystemAdmin": true, "reportCount": 850, "jobTitle": "Call Center Manager"}, {"userObjectId": "j7e8f9g0-h1i2-3d4e-5f6g-7h8i9j0k1l2m", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-20T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 150, "jobTitle": "Customer Service Representative"}, {"userObjectId": "k8f9g0h1-i2j3-4e5f-6g7h-8i9j0k1l2m3n", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-15T15:30:00Z", "dateUpdated": "2024-04-05T14:00:00Z", "isSystemAdmin": true, "reportCount": 700, "jobTitle": "Team Lead"}, {"userObjectId": "l9g0h1i2-j3k4-5f6g-7h8i-9j0k1l2m3n4o", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-20T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 450, "jobTitle": "Customer Service Representative"}, {"userObjectId": "m0h1i2j3-k4l5-6g7h-8i9j-0k1l2m3n4o5p", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-11-15T10:30:00Z", "dateUpdated": "2024-04-20T15:00:00Z", "isSystemAdmin": true, "reportCount": 800, "jobTitle": "Finance Manager"}, {"userObjectId": "n1i2j3k4-l5m6-7h8i-9j0k-1l2m3n4o5p6q", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Montreal", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-02-20T08:45:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 350, "jobTitle": "Customer Service Representative"}, {"userObjectId": "o2j3k4l5-m6n7-8i9j-0k1l-2m3n4o5p6q7r", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Calgary", "businessUnit": "Call Center Operations", "department": "Management", "isDeleted": false, "dateAdded": "2023-09-15T15:30:00Z", "dateUpdated": "2024-04-05T14:00:00Z", "isSystemAdmin": true, "reportCount": 950, "jobTitle": "Team Lead"}, {"userObjectId": "p3k4l5m6-n7o8-9j0k-1l2m-3n4o5p6q7r8s", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Ottawa", "businessUnit": "Call Center Operations", "department": "Customer Service", "isDeleted": false, "dateAdded": "2024-01-20T09:30:00Z", "dateUpdated": null, "isSystemAdmin": false, "reportCount": 700, "jobTitle": "Customer Service Representative"}, {"userObjectId": "q4l5m6n7-o8p9-0k1l-2m3n-4o5p6q7r8s9t", "email": "<EMAIL>", "fullName": "<PERSON>", "location": "Vancouver", "businessUnit": "Call Center Operations", "department": "Finance", "isDeleted": false, "dateAdded": "2023-11-15T10:30:00Z", "dateUpdated": "2024-04-20T15:00:00Z", "isSystemAdmin": true, "reportCount": 900, "jobTitle": "Finance Manager"}]