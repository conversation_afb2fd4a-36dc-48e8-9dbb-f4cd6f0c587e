export interface SelectInputInterface {
  className?: string;
  isRequired?: boolean;
  isMultiSelect?: boolean;
  label?: string;
  labelKey?: string;
  onSelectCallback: (selectedItem: SelectedItemInterface) => void;
  selectedItem?: SelectedItemInterface;
};

export interface SelectedItemInterface {
  label: string;
  value: any;
}

export interface HandleItemSelectionInterface {
  (item: SelectedItemInterface, event?: React.MouseEvent<HTMLElement>): void;
}
