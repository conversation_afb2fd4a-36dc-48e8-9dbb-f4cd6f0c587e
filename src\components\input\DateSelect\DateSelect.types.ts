import {
  ComponentAppearance,
  ComponentDateType,
  ComponentRounded,
  ComponentSize,
  ComponentTextSize
} from "../../../components/componentProperties/componentProperties.types";
import { TimeInMinutes } from "../../../global";

export interface DateSelectInterface {
  appearance?: ComponentAppearance
  className?: string;
  handleDateSelect: (date: Date) => void;
  isRequired?: boolean;
  label?: string;
  name: string;
  placeholder?: string;
  rounded?: ComponentRounded;
  selectedDate?: Date;
  size?: ComponentSize;
  textSize?: ComponentTextSize;
  timeInterval?: TimeInMinutes;
  type?: ComponentDateType;
};


export default DateSelectInterface;
