import { FieldMeta, FieldProps } from "../../../global";
import {
  ComponentAppearance,
  ComponentRounded,
  ComponentSize,
  ComponentTextSize
} from "../../componentProperties/componentProperties.types";

export type TextInputType = "text" | "number";

export interface TextInputInterface {
  appearance?: ComponentAppearance;
  icon?: string;
  iconClassName?: string;
  iconSubmit?: boolean;
  input?: FieldProps;
  inputClassName?: string;
  inputWrapperClassName?: string;
  meta?: FieldMeta;
  name?: string;
  onClick?: any;
  pattern?: string;
  placeholder?: string;
  rounded?: ComponentRounded;
  size?: ComponentSize;
  textSize?: ComponentTextSize;
  type?: TextInputType;
}
