import { CollapseProps } from 'antd'
import { Dispatch, SetStateAction } from 'react'
import { IIdleTimer } from 'react-idle-timer'
import { LabelResource } from 'src/api/proxy/TempProxy'
import { ManageReportsData, Pagination } from 'src/components/ReportTableManage/index.type'
import { UserInformation, UserInterface } from 'src/components/UserTableManage/index.type'
import { Category } from 'src/pages/index.type'

export interface ManageReportsPageContextType {
  reportList: any[]
  setReportList: Dispatch<SetStateAction<any[]>>
  pagination: Pagination
  setPagination: Dispatch<SetStateAction<Pagination>>
  totalAccount: number
  setTotalAccount: Dispatch<SetStateAction<number>>
  filters: {
    permissionValue: { id: string; name: string }
    dropDownValue: { id: string; name: string }
    isFilterActive: boolean
  }
  setFilters: Dispatch<SetStateAction<{ permissionValue: { id: string; name: string }; dropDownValue: { id: string; name: string }; isFilterActive: boolean }>>
  categories: any[]
  setCategories: Dispatch<SetStateAction<any[]>>
  reportsPerUser: number
  setReportsPerUser: Dispatch<SetStateAction<number>>
}

export interface UsersContextType {
  data: UserInformation[]
  setData: Dispatch<SetStateAction<UserInformation[]>>
  dataFiltered: UserInformation[]
  setDataFiltered: Dispatch<SetStateAction<UserInformation[]>>
}

export interface ReportPageContextType {
  reportId: string
  setReportId: Dispatch<SetStateAction<string>>
  reportGuid: string
  setReportGuid: Dispatch<SetStateAction<string>>
  workspaceGuid: string
  setWorkspaceGuid: Dispatch<SetStateAction<string>>
  reportName: LabelResource | undefined
  setReportName: any
  reportDescription: LabelResource | undefined
  setReportDescription?: any
  favoriteItems: any[]
  setFavoriteItems: Dispatch<SetStateAction<any[]>>
  selected: any
  setSelected: Dispatch<SetStateAction<any>>
  // showAccordion: boolean
  // setShowAccordion: Dispatch<SetStateAction<boolean>>
  // isPanelPinned: boolean
  // setIsPanelPinned: Dispatch<SetStateAction<boolean>>
}

export interface GlobalContextInterface {
  isAppAdmin: boolean
  setIsAppAdmin: (value: boolean) => void
  isReportAdmin: boolean
  setIsReportAdmin: (value: boolean) => void
  oid: string
  setOid: Dispatch<SetStateAction<string>>
  sessionTimeoutRef: React.MutableRefObject<NodeJS.Timeout | null>
  intervalRef: React.MutableRefObject<NodeJS.Timeout | null>
  modalIsOpen: boolean
  setIsModalOpen: Dispatch<SetStateAction<boolean>>
  isIdle: boolean
  setIsIdle: Dispatch<SetStateAction<boolean>>
  showAccordion: boolean
  setShowAccordion: Dispatch<SetStateAction<boolean>>
  isPanelPinned: boolean
  setIsPanelPinned: Dispatch<SetStateAction<boolean>>
}

export interface ManageCategoryPageContextType {
  categoryList: CollapseProps['items'] | []
  setCategoryList: (value: CollapseProps['items'] | []) => void
  isCollapsible: boolean
  setIsCollapsible: (value: boolean) => void
  apiResponseCategoryList: Category[]
  setApiResponseCategoryList: (value: Category[]) => void
}

export interface UserManagementPageContextInterface {
  userList: UserInterface[]
  setUserList: Dispatch<SetStateAction<UserInterface[]>>
  pagination: {
    totalPages: number
    pageIndex: number
    pageSize: number
  }
  setPagination: Dispatch<
    SetStateAction<{
      totalPages: number
      pageIndex: number
      pageSize: number
    }>
  >
  totalAccount: number
  setTotalAccount: Dispatch<SetStateAction<number>>
  filters: UserFiltersType
  setFilters: Dispatch<SetStateAction<UserFiltersType>>
  totalUsers: number
  setTotalUsers: Dispatch<SetStateAction<number>>
  businessUnits: any[]
  setBusinessUnits: Dispatch<SetStateAction<any[]>>
}

export interface UserFiltersType {
  isFilterActive: boolean
  permissions: {
    id: string
    name: string
  }
  businessUnits: {
    id: string
    name: string
  }
  qty?: {
    min: number
    max: number
  }
  status: {
    id: string
    name: string
  }
}
