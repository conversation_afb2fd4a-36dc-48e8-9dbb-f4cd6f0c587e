export type ReportType = {
  authMethod: number
  categoryId: string
  categoryNameEn: string
  categoryNameFr: string
  dateAdded: string
  descriptionEn: string | null
  descriptionFr: string | null
  externalAccessible: boolean
  id: string
  isPublic: boolean
  isReportAdmin: boolean
  nameEn: string
  nameFr: string
  powerBIReportId: string
  workSpaceID: string
}

export interface ReportList {
  totalCount: number
  totalPages: number
  pageNumber: number
  pageSize: number
  data: ReportType[]
  isError: boolean
  status: string
  statusText: string
}
