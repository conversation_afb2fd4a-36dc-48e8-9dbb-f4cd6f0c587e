import { TableProps } from "antd";

export interface UserInformation {
  name: string;
  lastname: string;
  email: string;
  status: string;
  call_center: string;
  permission: string;
  reports: number;
}

export interface UserInterface {
  userObjectId: string // GUID represented as a string
  email: string
  fullName: string
  location?: string | null // Optional and can be null
  businessUnit?: string | null // Optional and can be null
  department?: string | null // Optional and can be null
  isDeleted?: boolean
  dateAdded?: Date | null // Optional and can be null
  dateUpdated?: Date | null // Optional and can be null
  isSystemAdmin: boolean
  reportCount?: number
  jobTitle: string | null // Optional and can be null
}


export interface EditRecordType {
  user: UserInformation;
  URL: string;
  dataForm: dataFormType;
}

type dataFormType = {
  permission: string;
  status: string;
};

export type OnChange = NonNullable<TableProps<UserInformation>["onChange"]>;
export type Filters = Parameters<OnChange>[1];
export type GetSingle<T> = T extends (infer U)[] ? U : never;
export type Sorts = GetSingle<Parameters<OnChange>[2]>;
