import { ReactNode } from 'react'
import { User } from 'src/components/ReportTableManage/index.type'

export interface PageInterface {
  setHeaderContent?: (content: ReactNode) => void
  setHeaderTitle: (headerTitle: ReactNode) => void
}

export interface Category {
  id: number
  categoryEn: string
  categoryFr: string
  descriptionEn: string | null
  descriptionFr: string | null
  isActive: boolean
  isDeleted: boolean
  displayOrder: number
  dateAdded: string // You might want to use `Date` type if you parse it to a Date object.
  categoryGroupId: number
  categoryGroup: string | null
  reportCount: number
  reports: any | null // If you know the structure of `reports`, you can replace `any` with the specific type.
}

export type fetchReportListType = {
  extraquery?: any
}

export type ReportType = {
  id: string
  powerBIReportId: string
  workSpaceID: string
  nameEn: string
  nameFr: string
  descriptionEn: string | null
  descriptionFr: string | null
  authMethod: number
  externalAccessible: boolean
  isPublic: boolean
  dateAdded: string
  isReportAdmin: boolean
}

export type initialDataFormAddReport = {
  powerBiReportId: string
  workspaceId: string
  category: {
    id: string
    name: string
  }
  reportName: {
    en: string
    fr: string
  }
  description: {
    en: string
    fr: string
  }
  isPrivate: boolean
  authMethod: {
    id: string
    name: string
  }
}

export type NameAndDescriptionType = {
  dataForm: initialDataFormAddReport
  setDataform: React.Dispatch<React.SetStateAction<initialDataFormAddReport>>
  isValidationOn: boolean
}

export interface GraphUserRequestInfo {
  userObjectId: string
  email: string
  fullName: string
  isSystemAdmin: boolean
  department: string | null
  location: string
  businessUnit: string | null
  jobTitle: string
}

export interface ShareReportSearchResultsInterface {
  setShowResults: React.Dispatch<React.SetStateAction<boolean>>
  searchQuery: string
  setListOfTable: React.Dispatch<React.SetStateAction<User[]>>
  listOfTable: User[]
  handleReset: () => void
}

export enum TableName {
  ReportManagement = 'reportManagement',
  UserManagement = 'userManagement',
  CategoryManagement = 'categoryManagement',
  AuditLogs = 'auditLog',
  FrequentReport = 'frequentReport',
}