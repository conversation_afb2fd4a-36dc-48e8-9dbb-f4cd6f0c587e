export class PBIReport {
  description: string;
  reportGuid: string;
  reportName: string;
  workSpaceGuid: string;

  constructor(reportGuid: string, reportName: string, workSpaceGuid: string, description: string) {
    this.description = description;
    this.reportGuid = reportGuid;
    this.reportName = reportName;
    this.workSpaceGuid = workSpaceGuid;
  }
}

export interface PBIReportResolved {
  error?: any;
  pbiReport: PBIReport;
}

export class authResponse {
  hasAccess: boolean;

  constructor(hasAccess: boolean) {
    this.hasAccess = hasAccess;
  }
}
