import { <PERSON><PERSON>oader } from '../Loader'
import { FaUserSlash, FaUsersSlash } from 'react-icons/fa'
import { GlobalContext } from 'src/contextAPI/globalContext'
import { GroupTableColumns, UserTableColumns } from './UserAndGroupsTableColumns'
import { IoClose } from 'react-icons/io5'
import { Table } from 'antd'
import { useNotification } from 'src/hooks/useNotification'
import { UserAccessInfo, UserAndGroupAssignmentTableType } from './index.type'
import { useTranslation } from 'react-i18next'
import Button from '../input/Button'
import React, { useContext } from 'react'
import TextInputWithSearchIcon from '../input/TextInputWithSearchIcon'

function UserAndGroupAssignmentTable(
    {
        modalInfo,
        listOfTable,
        setListOfTable,
        searchUserInputValue,
        setSearchUserInputValue,
        callApiLoading,
        handleSearch,
        report,
        setMasterCopyListOfTable,
        masterCopyListOfTable
    }: UserAndGroupAssignmentTableType) {
    const { isAppAdmin } = useContext(GlobalContext)
    const { t } = useTranslation();
    const { notification, contextHolder } = useNotification()

    const handleClearSearch = (view: string) => {
        // Clear search input and reset the table to its master copy
        if (view === 'user') {
            setSearchUserInputValue({ ...searchUserInputValue, user: "" })
            setListOfTable({ ...listOfTable, user: masterCopyListOfTable.user })
            return
        }
        if (view === 'group') {
            setSearchUserInputValue({ ...searchUserInputValue, group: "" })
            setListOfTable({ ...listOfTable, group: masterCopyListOfTable.group })
            return
        }
    }

    return (
        <div className='w-full overflow-hidden min-h-[500px] assignment-user-group'>
            <div className="flex w-[1800px]">
                {/* ---USER SECTION --- */}
                <section className={`px-1 w-[852px] flex flex-col gap-5 transition-all ease-in-out duration-300 ${modalInfo.title.id === 1 ? "ml-0" : "-ml-[852px]"}`}>
                    <div className='w-full flex items-center gap-6' >
                        <div className='relative w-full px-1'>
                            <TextInputWithSearchIcon
                                border_radio={false}
                                handleChange={(event) => handleSearch({ event, assignmentType: 1 })}
                                placeholder={t('reportManagement.userAndGroups.placeholderSearch')}
                                value={searchUserInputValue.user}
                            />
                            <aside>
                                {
                                    searchUserInputValue.user.length > 0 &&
                                    <Button
                                        btnType="main"
                                        className="absolute right-3 top-[13px] rounded-full"
                                        icon={<IoClose size={12} />}
                                        handleClick={()=>handleClearSearch("user")}
                                    />
                                }
                            </aside>
                        </div>
                    </div>
                    <h2 className='font-semibold'>{t('reportManagement.userAndGroups.listUserTitle')}</h2>
                    <Table
                        columns={UserTableColumns({ setListOfTable, listOfTable, report, notification, setMasterCopyListOfTable })}
                        loading={{ indicator: <div><CircleLoader /></div>, spinning: callApiLoading }}
                        dataSource={listOfTable.user}
                        className='w-full'
                        rowKey={(record: UserAccessInfo) => record.user?.email || record.entraId}
                        showSorterTooltip={false}
                        pagination={false}
                        scroll={{ y: 350 }}
                        locale={{
                            emptyText() {
                                return <div className='flex justify-center items-center flex-col h-[250px]'><FaUserSlash size={40} /><h2>{t('reportManagement.userAndGroups.noUserAssigned')}</h2></div>
                            },
                        }}
                    />
                </section>
                {/* --- GROUP SECTION --- */
                    isAppAdmin &&
                    <section className='px-1 w-[852px] flex flex-col gap-5'>
                        <div className='relative w-full px-1'>
                            <TextInputWithSearchIcon
                                border_radio={false}
                                handleChange={(event) => handleSearch({ event, assignmentType: 2 })}
                                placeholder={t('reportManagement.userAndGroups.placeholderSearchGroup')}
                                value={searchUserInputValue.group}
                            />
                            <aside>
                                {
                                    searchUserInputValue.group.length > 0 &&
                                    <Button
                                        btnType="main"
                                        className="absolute right-3 top-[13px] rounded-full"
                                        icon={<IoClose size={12} />}
                                            handleClick={() => handleClearSearch("group")}
                                    />
                                }
                            </aside>
                        </div>
                        <h2 className='font-semibold'>{t('reportManagement.userAndGroups.listGroupTitle')}</h2>
                        <Table
                            columns={GroupTableColumns({ setListOfTable, listOfTable, report, notification, setMasterCopyListOfTable })}
                            loading={{ indicator: <div><CircleLoader /></div>, spinning: callApiLoading }}
                            dataSource={listOfTable.group}
                            className='w-full '
                            rowKey={(record: UserAccessInfo) => record.entraId}
                            showSorterTooltip={false}
                            pagination={false}
                            scroll={{ x: 'max-content', y: 350 }}
                            locale={{
                                emptyText() {
                                    return <div className='flex justify-center items-center flex-col h-[250px]'><FaUsersSlash size={40} /><h2>{t('reportManagement.userAndGroups.noGroupAssigned')}</h2></div>
                                },
                            }}
                        />
                    </section>
                }
            </div>
            {contextHolder}
        </div>
    )
}

export default UserAndGroupAssignmentTable