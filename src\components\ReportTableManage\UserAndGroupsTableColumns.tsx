import { GlobalContext } from 'src/contextAPI/globalContext'
import { LiaDownloadSolid } from "react-icons/lia";
import { Tooltip } from 'antd'
import { UserAccessInfo, UserAndGroupsTableColumnsType } from './index.type'
import { useTranslation } from 'react-i18next'
import React, { useContext } from 'react'
import useCallApi from 'src/hooks/useCallApi'

export function UserTableColumns({ setListOfTable, listOfTable, report, notification, setMasterCopyListOfTable }: UserAndGroupsTableColumnsType) {
    const { t } = useTranslation();
    const { oid, isAppAdmin } = useContext(GlobalContext);
    const { callApi } = useCallApi();
    const [loadingRows, setLoadingRows] = React.useState<Record<string, boolean>>({});

    const handleChange = async (record: UserAccessInfo) => {
        setLoadingRows(prev => ({ ...prev, [record.entraId]: true }));
        try {
            let objectToSend = {
                submittedByUserId: oid,
                isAdmin: !record.isReportAdmin,
                reportAccessId: record.id,
                reportId: report.id
            };

            let response = await callApi({
                endpoint: 'UpdateReportAdminAccess',
                body: objectToSend,
                method: 'POST'
            });

            if (response.status !== 200) {
                notification({
                    content: {
                        name: t("t2"),
                        message: `${response?.status}: ${response.statusText || response?.message}`
                    },
                    type: 'error'
                });
                return;
            }

            setListOfTable({
                ...listOfTable,
                user: listOfTable.user.map((item: UserAccessInfo) =>
                    item.entraId === record.entraId ? { ...item, isReportAdmin: !item.isReportAdmin } : item
                )
            });

            setMasterCopyListOfTable({
                ...listOfTable,
                user: listOfTable.user.map((item: UserAccessInfo) =>
                    item.entraId === record.entraId ? { ...item, isReportAdmin: !item.isReportAdmin } : item
                )
            });

            notification({
                content: {
                    name: t("success"),
                    message: t('reportManagement.userAndGroups.notificationSuccess')
                },
                type: 'success'
            });
        } finally {
            setLoadingRows(prev => ({ ...prev, [record.entraId]: false }));
        }
    };

    return [
        {
            title: t('reportManagement.userAndGroups.userName'),
            key: 'name',
            render: (record: any) => record?.user?.fullName || '',
            sorter: (a: any, b: any) => a.user?.fullName.localeCompare(b.user?.fullName),
        },
        {
            title: t('reportManagement.userAndGroups.email'),
            key: 'email',
            align: 'center' as 'center',
            render: (record: UserAccessInfo) => record?.user?.email || '',
            sorter: (a: any, b: any) => a.user?.email.localeCompare(b.user?.email)
        },
        {
            title: t('reportManagement.userAndGroups.admin'),
            key: 'admin',
            align: 'center' as 'center',
            width: "20%",
            sorter: (a: any, b: any) => a.isReportAdmin - b.isReportAdmin,
            defaultSortOrder: 'descend' as 'descend' | 'ascend' | undefined,
            render: (record: UserAccessInfo) => (
                <>
                    {loadingRows[record.entraId] ? (
                        <div className='flex justify-center items-center'>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#e81a3b]"></div>
                        </div>
                    ) : (
                        <input
                            className={`userAssignTable checkbox ${!isAppAdmin && oid === record.entraId ? '!cursor-not-allowed' : 'cursor-pointer'}`}
                            type='checkbox'
                            onChange={async () => await handleChange(record)}
                            checked={record.isReportAdmin}
                            disabled={!isAppAdmin && oid === record.entraId || loadingRows[record.entraId]}
                        />
                    )}
                </>
            )
        }
    ];
}

export function GroupTableColumns({ notification }: UserAndGroupsTableColumnsType) {
    const { t } = useTranslation();
    const { callApi } = useCallApi()
    const [loadingRows, setLoadingRows] = React.useState<Record<string, boolean>>({});

    const handleDowload = async (record: UserAccessInfo) => {
        console.log("j0;aa")
        const name = record.groupName || '';
        setLoadingRows(prev => ({ ...prev, [name]: true }));

        try {
            const response = await callApi({
                endpoint: 'ExportGroupMembers',
                query: name,
                contentType: "blob",
                isToExport: true
            });


            console.log("response",response)
            

            if (!(response instanceof ArrayBuffer)) {
                notification({
                    content: {
                        name: t("t2"),
                        message: `${response.status}: ${response.statusText || response.message}`
                    },
                    type: 'error'
                });
                return;
            }

            const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
            const outputFilename = `Insight Hub - ${name} - ${new Date().toLocaleDateString('en-CA')}.xlsx`;
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', outputFilename);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } finally {
            setLoadingRows(prev => ({ ...prev, [name]: false }));
        }
    }

    return [
        {
            title: t('reportManagement.userAndGroups.groupName'),
            key: 'group',
            render: (record: UserAccessInfo) => record.groupName || '',
            sorter: (a: any, b: any) => a.groupName.localeCompare(b.groupName),
        },
        {
            title: t('reportManagement.userAndGroups.actions'),
            key: 'action',
            align: 'center' as 'center',
            render: (record: UserAccessInfo) => (
                loadingRows[record.groupName || '']
                    ? <div className='flex justify-center items-center'>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#e81a3b]"></div>
                    </div>
                    : <div className='flex justify-center'>
                        <Tooltip placement="left" color='#4d4c4c' title={t('reportManagement.userAndGroups.downloadGroupUserList')}>
                            <LiaDownloadSolid
                                className='cursor-pointer text-[#857f7f] hover:text-red-500'
                                size={18}
                                onClick={async () => handleDowload(record)}
                            />
                        </Tooltip>
                    </div>
            )
        },
    ]
}