import { <PERSON>Loader } from '../Loader';
import { GlobalContext } from 'src/contextAPI/globalContext';
import { MdOutlineMarkEmailRead } from 'react-icons/md';
import { ReportPageContext } from 'src/contextAPI/reportPageContext';
import { useTranslation } from 'react-i18next';
import Button from '../input/Button';
import React, { useContext, useState } from 'react'
import useCallApi from 'src/hooks/useCallApi';
import { useNotification } from 'src/hooks/useNotification';

const RequestAccess = () => {
    const { t } = useTranslation();
    const { callApi, callApiLoading, data, isError } = useCallApi();
    const { reportId } = useContext(ReportPageContext);
    const { oid } = useContext(GlobalContext);
    const [statusRequest, setStatusRequest] = useState(false);
    const { contextHolder, notification } = useNotification()

    const reqBody = {
        reportId: reportId,
        submittedByUserId: oid,
        requestType: 1,
        reportAccessList: [
            {
                entraId: oid,
                email: "",
                accessType: 1,
                hasAccess: false,
                isReportAdmin: false,
                groupName: null
            }
        ]
    };

    const handleClick = async () => {
        let res = await callApi({ endpoint: 'RequestAccess', body: reqBody, method: 'POST' });
        if (res.status === 200) {
            setStatusRequest(true);
            notification({ content: { name: t("success"), message: t("reports.theRequestHasBeenSent") }, type: 'success' })
        } else {
            notification({ content: { name: t("t2"), message: `${res.status || res && res.message || res.statusText || isError && isError.message} ` }, type: 'error' })
        }
    };

    return (
        <div className='flex flex-col justify-center items-center'>
            {
                callApiLoading
                    ? <CircleLoader />
                    : statusRequest
                        ? <div className="flex items-center gap-1 py-[10px] px-6 bg-[#e2e5e7]">
                            <p className="proximaNova text-gray-600">{t('reports.requestSent')}</p>
                            <MdOutlineMarkEmailRead color="#4b5563" />
                        </div>
                        : <Button btnType="main" handleClick={handleClick} text={t("reports.requestAccess")} className='h-10 px-4' />
            }
            {contextHolder}
        </div>
    )
}
export default RequestAccess;