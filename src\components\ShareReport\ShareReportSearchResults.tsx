import { ShareReportSearchResultsInterface } from 'src/pages/index.type';
import { useClickAway } from '@uidotdev/usehooks';
import { useDebounce } from 'src/hooks/useDebounce';
import { useNotification } from 'src/hooks/useNotification';
import { User } from '../ReportTableManage/index.type';
import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react'
import useCallApi from 'src/hooks/useCallApi';

function ShareReportSearchResults({ setShowResults, searchQuery, setListOfTable, listOfTable, handleReset }: ShareReportSearchResultsInterface) {
    const { t } = useTranslation();
    const { callApiLoading, callApi, isError, data, setData } = useCallApi()
    const { notification, contextHolder } = useNotification();
    const debouncedSearch = useDebounce(searchQuery, 500);
    const [listOfEmails, setListOfEmails] = useState([] as User[])
    const ref: any = useClickAway(() => { setShowResults(false) });

    const handleSelect = async (foundUser: User) => {
        let userExist = listOfTable.some((user: User) => user.userObjectId === foundUser.userObjectId)
        if (userExist) {
            notification({ content: { name: t("t2"), message: t("reports.share.userAlreadyAdded") }, type: 'error' })
            return
        }
        setListOfTable([...listOfTable, foundUser])
        handleReset();
    }

    //Effect to handle search
    useEffect(() => { searchUsers(); }, [debouncedSearch]);
    //----Method to search users when debouncedSearch changes
    const searchUsers = async () => {
        if (debouncedSearch === "") {
            setData([]);
            setListOfEmails([]);
            return;
        }
        let res: []
        res = await callApi({ endpoint: "AssignmentSearchUser", query: debouncedSearch }); //--> API TO CALL TO GET THE LIST OF USERS
        setListOfEmails(res);
    }
    return (
        <>
            {searchQuery !== "" && debouncedSearch !== "" && (
                <ul ref={ref} className="absolute bg-[#fbfbfb] w-full max-h-96 overflow-y-auto scroll-smooth scroll-thin  z-10 general-shadow m-0 p-0">
                    {
                        callApiLoading
                            ? <li className="w-full py-3 pl-3 pr-2 cursor-pointer text-sm">{t("loading")}</li>
                            : <>
                                {
                                    data.length === 0 && searchQuery !== "" && (
                                        <li className="w-full py-3 pl-3 pr-2 cursor-pointer text-sm">{t("no_Matching_Results")}</li>
                                    )}
                                {
                                    listOfEmails.map((user: User) => (
                                        <li
                                            key={user.userObjectId}
                                            className="w-full py-3 pl-3 pr-2 cursor-pointer hover:text-[#ed1a3b] hover:bg-[#fff2ee] text-sm"
                                            onClick={async () => await handleSelect(user)}>
                                            {user?.email || "No email"}
                                        </li>
                                    ))
                                }
                            </>
                    }
                </ul>
            )}
            {contextHolder}
        </>
    )
}
export default ShareReportSearchResults