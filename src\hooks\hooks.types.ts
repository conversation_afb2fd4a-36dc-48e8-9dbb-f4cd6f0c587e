import { MessageType } from 'antd/es/message/interface'
import { LabelResource } from 'src/api/proxy/TempProxy'

export type useUserFilterType = {
  VALUES_STATUS: valuesStatusType[]
}

type valuesStatusType = {
  name: string
  value: boolean
}

export interface useDataHandlerType<T> {
  context: React.Context<T | undefined>
}

export type useNotificationType = {
  type: 'success' | 'error' | 'info' | 'warning'
  content: any
}

export interface MenuItemMetaDataInterface {
  reportGuid: string | undefined
  reportId: string | undefined
  reportName: LabelResource | undefined
  reportDescription: LabelResource | undefined
  isFavorite?: boolean | undefined
}

export interface useCallApiType {
  endpoint:
    | 'AllReports'
    | 'AddBulkUsers'
    | 'AddCategory'
    | 'AddReport'
    | 'AssignmentList'
    | 'AssignmentSearchUser'
    | 'AssignmentSearchGroup'
    | 'AssignmentUpdateUserAndGroup'
    | 'AuditLogs'
    | 'AuditLogsExport'
    | 'Categories'
    | 'CategorySearch'
    | 'BusinessUnits'
    | 'DeleteCategory'
    | 'DeleteReport'
    | 'DeleteUser'
    | 'ExportGroupMembers'
    | 'Favorites'
    | 'GlobalSearchReport'
    | 'GraphUsers'
    | 'IsAuthorized'
    | 'IsAssignedToPbiService'
    | 'OneReport'
    | 'RequestAccess'
    | 'ReportList'
    | 'ReportUsage'
    | 'SearchList'
    | 'SearchUserForAccess'
    | 'SpReportToken'
    | 'UpdateCategory'
    | 'UpdateReport'
    | 'UpdateReportAdminAccess'
    | 'UpdateUser'
    | 'UserFavorites'
    | 'UserList'
  query?: string
  body?: any
  extraHeaders?: any
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE',
  contentType?: string,
  isToExport?: boolean
}

export interface fetchCategoryListInterface {
  notification: ({ content, type }: useNotificationType) => MessageType
  setApiResponseCategoryList?: any
}
