import { useCallApiType } from 'src/hooks/hooks.types'

export type ReportType = {
  id: string
  powerBIReportId: string
  workSpaceID: string
  nameEn: string
  nameFr: string
  descriptionEn: string | null
  descriptionFr: string | null
  authMethod: number
  externalAccessible: boolean
  isPublic: boolean
  dateAdded: string
  isReportAdmin: boolean
  categoryNameEn: string
  categoryNameFr: string
  categoryId: string
}

export interface ManageReportsData {
  totalCount: number
  totalPages: number
  pageNumber: number
  pageSize: number
  data: ReportType[]
  isError: boolean
  status: string
  statusText: string
}

export type Pagination = {
  totalPages: number
  pageIndex: number
  pageSize: number
}

export interface ReportTableManageProps {
  callApi: ({ endpoint, query }: useCallApiType) => Promise<any>
  callApiLoading: boolean
}

export interface ReportListRequest {
  pageIndex: number
  pageSize: number
  categoryId?: number | null
  isPrivate?: boolean | null
  searchFilter?: string | null
  sortBy?: string | null
  ascending?: boolean
}

export interface EditModuleType {
  report: ReportType
}

export type initialDataFormEditReport = {
  powerBiReportId: string
  workspaceId: string
  category: {
    id: string
    name: string
  }
  reportName: {
    en: string
    fr: string
  }
  description: {
    en: string
    fr: string
  }
  isPrivate: boolean
  authMethod: {
    id: string
    name: string
  }
}

interface ModalInfoType {
  isOpen: boolean
  title: {
    id: number
    name: string
  }
}
export interface UserAndGroupAssignmentTableType {
  modalInfo: ModalInfoType
  listOfTable: responseFetchAccessList
  setListOfTable: (value: responseFetchAccessList) => void
  setMasterCopyListOfTable: (value: responseFetchAccessList) => void
  masterCopyListOfTable: responseFetchAccessList
  searchUserInputValue: { user: string; group: string }
  setSearchUserInputValue: (value: { user: string; group: string }) => void
  callApiLoading: boolean
  handleSearch: ({ event, assignmentType }: handleSearchUserAndGroupType) => void
  report: ReportType
}

export type UserAndGroupsTableColumnsType = {
    setListOfTable: (value: responseFetchAccessList) => void,
    listOfTable: responseFetchAccessList,
    report: ReportType
    notification: (value: { content: { name: string, message: string }, type: 'success' | 'error' }) => void
    setMasterCopyListOfTable: (value: responseFetchAccessList) => void
}

export interface UserAndGroupAssignmentType {
  report: ReportType
}

export interface User {
  userObjectId: string
  email: string
  fullName: string
  location: string | null
  businessUnit: string | null
  department: string | null
  isActive: boolean
  isSystemAdmin: boolean
  dateAdded: string | null
  dateUpdated: string | null
  dateSynced: string | null
  userAccessLogs: any | null
  favorites: any | null
  isDeleted: boolean
}

export interface UserAccessInfo {
  entraId: string
  email?: string
  accessType: number
  hasAccess: boolean
  isReportAdmin: boolean
  groupName: string | null
  user?: User | null
  id:number
}

export type responseFetchAccessList = {
  user: UserAccessInfo[] | []
  group: UserAccessInfo[] | []
}

export type handleSearchUserAndGroupType = {
  event: React.ChangeEvent<HTMLInputElement>
  assignmentType: number
}

export interface UserSearchResultProps {
  searchQuery: string
  setSearchQuery: (value: { user: string; group: string }) => void
  setSearchUserInputValue: (value: { user: string; group: string }) => void
  assignmentType: number
  listOfTable: responseFetchAccessList
  setListOfTable: (value: responseFetchAccessList) => void
  checkBoxValue?: boolean
  setShowResults: (value: { user: boolean; group: boolean }) => void
  report: ReportType
}

export interface UserGroupCountInterface {
  groupCount: number
  userCount: number
}
