import { HandleItemSelectionInterface, SelectInputInterface, SelectedItemInterface } from "../../SelectInput.types";
import {
  ComponentAppearance,
  ComponentRounded,
  ComponentSize,
  ComponentTextSize,
  DropdownTopArrowPosition,
} from "src/components/componentProperties/componentProperties.types";

export interface SingleSelectDropdownInterface {
  appearance?: ComponentAppearance;
  borderArrowPosition?: DropdownTopArrowPosition;
  children?: React.ReactNode;
  classNameContainer?: string;
  classNameInput?: string;
  handleInputFocus: () => void;
  handleInputBlur: () => void;
  handleItemSelection: HandleItemSelectionInterface;
  isContentVisible?: boolean;
  isDisabled?: boolean;
  items: any[];
  labelKey?: string;
  name?: string;
  placeholder?: string;
  rounded?: ComponentRounded;
  selectedItem?: SelectedItemInterface;
  showInput?: boolean;
  size?: ComponentSize;
  textSize?: ComponentTextSize;
  useArrowBorder?: boolean;
}
