import { useEffect, useState } from "react";
import AllReportsSearch from "../../pageContent/allReports/AllReportsSearch";
import HeaderWrapper from "../../components/layout/header/HeaderWrapper";
import MaxWidthWrapper from "../../components/page/MaxWidthWrapper";
import { PageInterface } from "../index.type";
import TextInput from "src/components/input/TextInput/TextInput";
import { Form } from "react-final-form";
import { useTranslation } from "react-i18next";
import { getReportsMenu } from "../../api/resource/reportDataResource";
import { ReportsMenuItemResource } from "src/api/proxy/TempProxy";

const DiscoverReports = ({ setHeaderContent, setHeaderTitle }: PageInterface) => {
  const { t } = useTranslation();
  const [reportsMenuData, setReportsMenuData] = useState<ReportsMenuItemResource[]>([]);

  useEffect(() => {
    setHeaderContent && setHeaderContent(createSearchButton());
    setHeaderTitle("bdoReports_title");
    setReportsMenuData(getReportsMenu());
  }, []);

  const handleOnSubmitSearchButton = async (values: any) => {
    console.log(values);
  };

  const handleSetOpenAccordion = () => {};

  const createSearchButton = () => {
    return (
      <div className="flex pr-6 items-center">
        <Form
          onSubmit={handleOnSubmitSearchButton}
          validate={() => console.log}
          render={({ handleSubmit, values }: any) => (
            <form onSubmit={handleSubmit}>
              <TextInput
                inputClassName="text-base"
                icon="search"
                iconSubmit
                inputWrapperClassName=""
                name="testTextInput"
                placeholder="enter some input"
                size="sm"
              />
            </form>
          )}
        />
      </div>
    );
  };

  return (
    <div className="flex w-full h-full">
      <MaxWidthWrapper className="flex-col">
        <HeaderWrapper className="bg-bdo-bg-04-white">
          <div
            className={`
            text-3xl font-bold 
            text-bdo-text-01-charcoal
          `}
          >
            {t("discoverReports_title")}
          </div>
        </HeaderWrapper>
        {/* <AllReportsSearch
          classNameAccordionWrapper="flex-col w-48"
          reportsMenuData={reportsMenuData}
          // setShowAccordion={handleSetOpenAccordion}
        /> */}
      </MaxWidthWrapper>
    </div>
  );
};

export default DiscoverReports;
