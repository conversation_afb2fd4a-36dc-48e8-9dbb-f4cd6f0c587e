import { useClickAway } from '@uidotdev/usehooks';
import React, { useState } from 'react'
import { useTranslation } from "react-i18next";
import { IoClose } from 'react-icons/io5';
import { useLocation, useNavigate } from 'react-router-dom';
import Button from 'src/components/input/Button';
import TextInputWithSearchIcon from 'src/components/input/TextInputWithSearchIcon';
import ReportSearch from 'src/components/layout/navigation/Accordion/ReportSearch';

const GlobalSearchReport = () => {
    const { t } = useTranslation();
    const location = useLocation();
    const [searchQuery, setSearchQuery] = useState("");
    const [showResults, setShowResults] = useState(false)
    const [searchInputValue, setSearchInputValue] = useState("");
    const ref: any = useClickAway(() => { setShowResults(false) });
    const navigate = useNavigate();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchInputValue(e.target.value);
        setSearchQuery(e.target.value);
        setShowResults(true);
    };

    const handleResetClick = () => {
        setSearchInputValue("");
        setSearchQuery("");
        if (location.pathname === "/reports") {
            navigate('/reports');
        }
    }

    return (
        <section ref={ref} className='max-w-[392px] min-w-[392px] z-10'>
            <div className="flex relative">
                <TextInputWithSearchIcon handleChange={handleChange} placeholder={`${t("reports.globalReportSearch")}`} border_radio={false} value={searchInputValue} />
                <aside>
                    {searchInputValue.length > 0 &&
                        <Button
                            btnType="main"
                            className="absolute right-2 top-[13px] rounded-full"
                            icon={<IoClose size={12} />}
                            handleClick={handleResetClick}
                        />
                    }
                </aside>
            </div>
            {showResults && <ReportSearch searchQuery={searchQuery} getMenuData={() => { }} setShowResults={setShowResults} urlToCall='GlobalSearchReport' />}
        </section>
    )
}

export default GlobalSearchReport;