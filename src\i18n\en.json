{"translation": {"all": "All", "reports_header": "Analytics", "search_placeholder": "Search", "addView_title": "ADD VIEW", "administration_title": "ADMINISTRATION", "allCategories_title": "ALL CATEGORIES", "allReports_title": "ALL REPORTS", "allViews_title": "ALL VIEWS", "bdoReports_title": "Reports", "create": "Create", "createSupportTicket": "Create Support Ticket", "discoverReports_title": "DISCOVER REPORTS", "delete": "Delete", "favourites_title": "FAVOURITES", "firmwideServices_title": "FIRM-WIDE SERVICES", "industries_title": "INDUSTRIES", "loading": "Loading...", "login": "<PERSON><PERSON>", "myServiceNowTickets": "My AssistMe tickets", "no_Matching_Results": "No matching results", "reports_title": "REPORTS", "reportAccess": "Report Access", "serviceLines_title": "SERVICE LINES", "settings": "Settings", "share": "Share", "signout_title": "SIGN OUT", "submit_title": "SUBMIT", "success": "Success", "supportTicket_title": "SUPPORT TICKET", "sesionExpired_title": "Session Timeout", "sessionExpiredMessage": "Your session is about to expire.", "sessionExpiredButton": "Reset", "unauthorized_title": "Unauthorized Access", "unauthorizedMessage": "Your account does not have the required privileges to access this report.", "update": "Update", "bdoLodoTitle": "Insight Hub", "tooltipLenguage": "Change to French", "tooltipLegacyIcon": "Go back to the Legacy Analytics app", "topBar": {"legacyAnalyticsApp": "Legacy Analytics App", "help": "Help", "FAQ": "FAQ", "FAQlink": "https://bdocanada.sharepoint.com/:w:/t/IF-DataVisualizationandAnalysis/EWpAsE-Fk0hDqFI_7EOiJUwBqoDxVfARedOUlUAMgYLbOg?e=rlZ9r2", "video": "Video", "videoLink": "https://bdocanada.sharepoint.com/:v:/t/IF-DataVisualizationandAnalysis/Ef3X1sA9kIpAg0tW8YKrBTgBVdDEG3ZCknOiwi0iWUGVOw?e=B9NsqA", "referenceGuide": "Reference Guide", "referenceGuideLink": "https://bdocanada.sharepoint.com/:b:/t/IF-DataVisualizationandAnalysis/Eam3eH7m3NFHlgzvKxYeMrcBbovT_CkLABU-hxfAp-3hug?e=kKwTO4"}, "reports": {"globalReportSearch": "Global Report search", "inputPlaceholder": "Search", "inputPlaceholderGlobalSearch": "Global report search", "NoMartchingResults": "No matching results", "requestAccess": "Request Access", "requestSent": "Request Sent", "theRequestHasBeenSent": "The request has been sent", "title": "Reports", "noReportsAvailable": "No reports available", "weSorryButItLooksLike": "We're sorry, but it looks like there are no reports currently shared with you. Please check back later or submit a ticket for assistance.", "favorite": {"added": "Report added to favourites", "removed": "Report removed from favourites", "limit": "You can only mark up to 10 reports as favourites.", "tootipRemove": "Remove from favourites", "favorites": "Favourites"}, "share": {"shareReport": "Share Report", "share": "Share", "cancel": "Cancel", "userAlreadyAdded": "This user is already on the list", "noUsersSelectedForsharing": "No users selected for sharing."}, "unpinthemenu": "Unpin the menu to hide it", "pinthemenu": "Pin the menu to keep it visible", "menuCanNotbeCollapsed": "The menu cannot be collapsed while pinned", "collapseTheMenu": "Collapse the menu"}, "auditLogs": {"activityReportsOverview": "Activity & Reports Overview", "actionType": "Action Type", "count": "User Count", "details": "Details", "email": "User Email", "exportToPdf": "Export Excel", "leastAccessed": "Least Accessed", "mostAccessed": "Most Accessed", "timeStamps": "Date/Time", "title": "<PERSON><PERSON>", "frequentlyAccess": "Frequently Accessed Reports", "from": "From", "to": "To", "search": "Search", "searchByUserEmail": "Search by User <PERSON>", "filters": "Filters", "reset": "Reset", "all": "All", "user": "User", "userLogin": "User Login", "report": "Report", "action": "Action", "date": "Date", "noLogsAvailable": "No logs available", "searchLogs": "Search by User, Report or Action", "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "share": "Share", "add": "Add", "remove": "Remove", "login": "<PERSON><PERSON>", "logout": "Logout"}}, "userManagement": {"all": "All", "active": "Active", "activeUsers": "Active Users", "admin": "Admin", "inactive": "Inactive", "pagetitle": "User Management", "reportCount": "Report Count", "searchUserByEmail": "Search by First Name, Last Name or Email", "filters": {"status": "Status", "permission": "Permission", "businessUnit": "Business Unit", "reportsqtty": "Reports Qty", "apply": "Apply", "filters": "Filters", "reset": "Reset"}, "addUser": {"add": "Add", "title": "Add User", "listEmpty": "The list is empty.", "addedSuccessfully": "User added successfully.", "userNotFound": "User not found", "invalidEmail": "Invalid email", "emailAlreadyInTheList": "This user is already on the temporary list.", "emailAlreadyOnTheApp": "This user is already on the application.", "save": "Save", "search": "Search", "searchUserByEmail": "Search user by email", "cancel": "Cancel", "tolltipAdd": "Add User to the temporary list", "makeUserAsAdmin": "Make this user an Admin", "temporaryList": "List of users to be added"}, "editUser": {"title": "Edit User", "disableUser": "Disable User", "updatedSuccessfully": "User updated successfully.", "makeUserAsAdmin": "Make this user an Admin", "selectPermission": "Select Permission"}, "deleteUser": {"title": "Disable User", "deletedSuccessfully": "User disabled successfully.", "message": "Are you sure you want to disable this User?"}, "jobProfile": "Job Profile", "user": "User", "t3": "Matching Results", "t4": "Filters", "t6": "Apply", "t7": "Active", "t8": "Disabled", "t9": "Locked", "t10": "Pending", "t11": "All", "t12": "Status", "t13": "All", "t14": "User", "t15": "Admin", "t16": "Permission", "t17": "Reports Qty", "t18": "Min", "t19": "Max", "t20": "Report Users", "t21": "Add User", "t22": "User", "t23": "Status", "t24": "Business Unit", "t25": "Permission", "t26": "Reports", "t27": "Actions", "t28": "Delete User", "t29": "Are you sure you want to delete this User?", "t30": "Yes", "t31": "No", "t32": "Edit User", "t33": "Ok", "t34": "Cancel"}, "categManagement": {"t1": "Category Management", "listOfCategories": "List of Categories", "name": "Name", "description": "Description", "actions": "Actions", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "addCategory": "Add Category", "areYouSure": "Are you sure you want to delete this Category?", "addNewCategory": "Add New Category", "englishCategory": "English Category Name", "frenchCategory": "French Category Name", "englishDescription": "English Category Description", "frenchDescription": "French Category Description", "addedSuccessfully": "Category added successfully.", "updatedSuccessfully": "Category updated successfully.", "deletedSuccessfully": "Category deleted successfully.", "cannotDelete": "The Category cannot be deleted because there are Reports mapped to it", "info": "Fill out the mandatory fields in both English and French tabs before submitting.", "placeHolderEnterCategoryEnglishName": "Enter English Category Name", "placeHolderEnterCategoryFrenchName": "Enter French Category Name", "placeHolderEnterCategoryEnglishDescription": "Enter English Category Description", "placeHolderEnterCategoryFrenchDescription": "Enter French Category Description", "noReportsAvailable": "Error: No reports available for this category"}, "reportManagement": {"t1": "Report Management", "t2": "Add Report", "t3": "Report Names", "searchPlaceHolder": "Search by Report Name", "reports": "Reports", "table": {"titleName": "Report Name", "name": "nameEn", "titleDescription": "Description", "description": "descriptionEn", "titleCategory": "Category", "category": "categoryNameEn", "count": "User Count", "titleAccess": "Access", "addedOn": "Added On", "private": "Private", "public": "Public"}, "filters": {"access": "Access", "all": "All", "public": "Public", "private": "Private", "category": "Category", "categoryLn": "categoryEn", "activeInfo": "Search results will be filtered based on the selected criteria.", "reset": "Reset"}, "landing": {"title": "Report Viewer", "message": "Select a report to view its content here."}, "editmodule": {"title": "Edit Report", "titleName": "Name", "name": "nameEn", "titleDescription": "Description", "description": "descriptionEn", "access": "Access", "authMethod": "Authentication Method", "userContext": "User Context", "servicePrinciple": "Service Principal", "namePlaceholder": "Enter Report Name", "descriptionPlaceholder": "Enter Report Description", "accept": "Accept", "cancel": "Cancel", "updatedSuccessfully": "Report updated successfully."}, "deletemodule": {"title": "Delete Report", "deletedSuccessfully": "Report deleted successfully.", "message": "Are you sure you want to delete this Report?", "yes": "Yes", "no": "No"}, "addmodule": {"accept": "Accept", "access": "Access", "addedSuccessfully": "Report added successfully.", "authMethod": "Authentication Method", "alreadyExists": "This report is already added", "cancel": "Cancel", "category": "Category", "categoryPlaceholder": "Select Category", "description": "descriptionEn", "fieldRequired": "This field is required", "name": "nameEn", "namePlaceholder": "Enter Report Name", "makePrivate": "Make this Report private", "powerbi": "Power BI Report ID", "powerbiPlaceholder": "Enter Power BI Report ID", "reportAlreadyExists": "This report already exists", "powerbiReportNotFound": "Power BI Report not found", "servicePrinciple": "Service Principal", "title": "Add Report", "titleDescription": "Description", "titleName": "Name", "userContext": "User Context", "workspaceId": "Power BI Workspace ID", "workspaceIdPlaceholder": "Enter Power BI Workspace ID", "wrongGuid": "The Power BI Report ID or Workspace ID are not valid", "nameAndDescription": {"info": "Fill out the mandatory fields in both English and French tabs before submitting.", "labelEn": "English Report Name", "placeholderEn": "Enter English Report Name", "labelFr": "French Report Name", "placeholderFr": "Enter French Report Name", "descriptionPlaceholder": "Enter Report Description", "labelDescriptionEn": "English Report Description", "labelDescriptionFr": "French Report Description"}}, "userAndGroups": {"title": "User and Groups Assignments", "userTitle": "User Assignment", "groupTitle": "Group Assignment", "user": "User", "group": "Group", "assignmentBy": "Assignment By", "placeholderSearch": "Search User by email", "placeholderSearchGroup": "Search by Active Directory Group Name", "makeUserAdmin": "Make this user a report Admin", "listUserTitle": "List of User with access", "listGroupTitle": "List of Group with access", "noUserAssigned": "No user assigned", "noGroupAssigned": "No group assigned", "userName": "User Name", "groupName": "Group Name", "actions": "Action", "email": "Email", "admin": "Report Admin", "removeAccess": "Remove Access", "cancel": "Cancel", "save": "Save", "notificationSuccess": "Report updated succesfully", "userMustBeAssignedFirst": "User assignment to the PBI service is required before proceeding", "groupMustBeAssignedFirst": "Group assignment to the PBI service is required before proceeding", "listIsEmpty": "No records have been added to the list.", "downloadGroupUserList": "Download group user list"}}, "t1": "Sign Out", "t2": "Error", "t3": "Record deleted successfully.", "t4": "Success Operation: ", "t5": "Record Updated successfully.", "t6": "Record Added successfully.", "t7": "Submit", "t8": "Cancel", "save": "Save"}}