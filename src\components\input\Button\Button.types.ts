import { ReactNode } from "react";
import {
  ComponentAppearance,
  ComponentRounded,
  ComponentSize,
  ComponentTextSize
} from "../../componentProperties/componentProperties.types";

export type ButtonType = "button" | "submit";

export interface ButtonInterface {
  appearance?: ComponentAppearance;
  children: ReactNode;
  className?: string;
  onClick?: any;
  rounded?: ComponentRounded;
  size?: ComponentSize;
  textSize?: ComponentTextSize;
  type?: ButtonType;
  value?: string;
}

export type ButtonBdo = {
  text?: string
  icon?: ReactNode
  className?: string
  btnType: 'main' | 'secondary'
  handleClick?: () => void
}
