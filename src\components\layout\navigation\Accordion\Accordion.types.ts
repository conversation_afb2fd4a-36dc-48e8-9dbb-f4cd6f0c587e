import { AuthResource, LabelResource, ReportMenuTargetResource } from 'src/api/proxy/TempProxy'
import { ComponentAppearance } from 'src/components/componentProperties/componentProperties.types'
import { AccordionSearchReportsInterface } from './AccordionSection.types'

export interface AccordionInterface {
  appearance?: ComponentAppearance
  items?: MenuItemInterface[]
  title?: string
  selected?: string
  setShowAccordion?: any
  showAccordion?: boolean
  isError?: { status: boolean; message: string }
  isLoading?: boolean
  getMenuData: (itemClicked?: AccordionSearchReportsInterface) => void
}

export interface AccordionItemInterface {
  children?: AccordionItemInterface[]
  onClick: () => void
  label: string
}

export interface MenuItemInterface {
  auth?: AuthResource
  category?: LabelResource
  label?: LabelResource
  onClick: () => void
  target?: ReportMenuTargetResource
  children?: MenuItemInterface[]
  id: string
  isFavorite?: boolean
}
