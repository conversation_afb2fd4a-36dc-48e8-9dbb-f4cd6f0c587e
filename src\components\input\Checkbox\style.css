input[type="checkbox"].checkbox {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #ccc;
    background-color: #fff;
    cursor: pointer;
}

input[type="checkbox"].checkbox:checked {
    border-color: red;
    background-color: red;
}

input[type="checkbox"].checkbox::before {
    content: "";
    display: inline-block;
    width: 9px;
    height: 5px;
    border: 2px solid #fff;
    border-top: none;
    border-right: none;
    transform: rotate(315deg);
    position: relative;
    left: 2px;
    top: -7px;
}