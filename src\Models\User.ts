export class User {
  people_ID: number;
  emailAlias: string;
  firstName: string;
  hriS_ID: string;
  lastName: string;
  name: string;

  constructor(people_ID: number, hriS_ID: string, lastName: string, firstName: string,
      emailAlias: string, name: string) {
    this.emailAlias = emailAlias;
    this.firstName = firstName;
    this.hriS_ID = hriS_ID;
    this.lastName = lastName;
    this.name = name;
    this.people_ID = people_ID;
  }
}

export interface UserReport {
  userObjectId: string // GUID represented as a string
  email: string
  fullName: string
  location?: string | null // Optional and can be null
  businessUnit?: string | null // Optional and can be null
  department?: string | null // Optional and can be null
  isActive: boolean
  dateAdded?: Date | null // Optional and can be null
  dateUpdated?: Date | null // Optional and can be null
  isAdmin: boolean
  reportCount: number
}