import { Tooltip as ReactTooltip } from "react-tooltip";
import { TooltipInterface } from "./Tooltip.types";
import Icon from "../Icon/Icon";
import { ReactNode, useEffect, useState } from "react";
import TooltipContent from "./TooltipContent";
import {
  FONT_SIZE_VALUE_ONLY,
  LINE_HEIGHT_VALUE_ONLY,
  ROUNDED_VALUE_ONLY,
  WIDTH_VALUE_ONLY
} from "../commonComponentStyles";

const ToolTip = ({
  items,
  dataTooltipId,
  place = "top",
  rounded = "lg",
  size = "default",
  textSize = "default"
}: TooltipInterface) => {
  const [tooltips, setTooltips] = useState<ReactNode>();

  useEffect(() => {
    if (Array.isArray(items)) {
      const tooltips = items.filter((item) => item && item.tooltip)
        .map((item, index) => (
          <TooltipContent
            key={index}
            label={item.label}
            textSize={textSize}
            tooltip={item.tooltip}
          />
        ));
      tooltips.length > 0 && setTooltips(<div>{tooltips}</div>);
    }
  }, []);

  return (
    <>
      <span className="tooltip-wrapper">
        <span
          data-tooltip-id={dataTooltipId}
          data-tooltip-place={place}
        >
          <Icon
            className="text-sm text-bdo-text-01-charcoal ml-1 align-middle"
            name="info"
          />
        </span>
        <ReactTooltip
          id={dataTooltipId}
          className="tooltip"
          style={{
            border: "1px solid #D0D5DD", // air-gray-300
            borderRadius: ROUNDED_VALUE_ONLY[rounded],
            backgroundColor: "#FFF",
            font: "inherit",
            fontSize: FONT_SIZE_VALUE_ONLY[textSize],
            fontWeight: 500,
            lineHeight: LINE_HEIGHT_VALUE_ONLY[textSize],
            width: WIDTH_VALUE_ONLY[size],
          }}
        >
          {tooltips}
        </ReactTooltip>
      </span>
    </>
  );
};

export default ToolTip;
