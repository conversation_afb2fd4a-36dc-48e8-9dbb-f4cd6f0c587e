/* userandgroup assignment */

.shareReport .ant-modal-footer button:first-child {
  width: 100px;
  height: 30px;
  background-color: var(--secondaryColor);
  border: none;
  color: var(--textColor);
}

.shareReport .ant-modal-footer button:last-child {
  width: 120px;
  height: 30px;
  background-color: var(--primaryColor);
  border: none;
  color: var(--white);
}

.shareReport .ant-modal-footer button:first-child:hover,
.shareReport .ant-modal-footer button:last-child:hover {
  background-color: #cccccc54 !important;
  color: var(--primaryColor) !important;
  border: 0.1px solid var(--primaryColor) !important;
}

.shareReport {
  font-family: 'ProximaNova';
}

.shareReport .ant-modal-footer button {
  border-radius: 2px;
}

.shareReport .ant-modal-footer {
  height: 60px !important;
  display: flex;
  justify-content: end;
  align-items: end;
}

.shareReport .ant-select {
  border: 0.5px solid #00000026;
  width: 100%;
  height: 30px;
  cursor: pointer;
  padding: 0px 5px;
}

.shareReport select:focus-visible {
  border: none;
}

.shareReport select .shareReport select:focus-visible option {
  border-radius: 0%;
}

.shareReport input[type='checkbox'].checkbox {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid #ccc;
  background-color: #fff;
  cursor: pointer;
}

.shareReport input[type='checkbox'].checkbox:checked {
  border-color: red;
  background-color: red;
}

.shareReport input[type='checkbox'].checkbox::before {
  content: '';
  display: inline-block;
  width: 9px;
  height: 5px;
  border: 2px solid #fff;
  border-top: none;
  border-right: none;
  transform: rotate(315deg);
  position: relative;
  left: 2px;
  top: -8px;
}

.height-modal {
  top: calc(50% - 400px);
}

span[aria-label='close'] {
  color: #666;
}
button[aria-label='Close']:hover span span svg {
  color: var(--primaryColor);
}

.shareReport input[type='checkbox'].checkbox:disabled {
  background-color: #8080807d !important;
  border-color: #80808000 !important;
}
.addModule input[type='checkbox'].checkbox:disabled {
    background-color: #8080807d !important;
  border-color: #80808000 !important;
}