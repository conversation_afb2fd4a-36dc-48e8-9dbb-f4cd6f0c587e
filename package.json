{"name": "clientappv2", "version": "2.0.1", "private": true, "dependencies": {"@azure/msal-browser": "^2.38.0", "@azure/msal-react": "^1.5.9", "@heroicons/react": "^2.0.18", "@react-input/mask": "^1.2.7", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.36", "@types/react": "^18.2.12", "@types/react-datepicker": "^4.11.2", "@types/react-dom": "^18.2.5", "@types/uuid": "^9.0.2", "@uidotdev/usehooks": "^2.3.1", "antd": "^5.16.2", "axios": "^1.4.0", "eslint": "^8.57.0", "i18next": "^23.2.10", "i18next-browser-languagedetector": "^7.1.0", "openapi-client-axios-typegen": "^7.2.1", "powerbi-client-react": "^1.4.0", "powerbi-models": "^1.12.6", "powerbi-report-authoring": "^2.0.0", "react": "^18.2.0", "react-datepicker": "^4.14.1", "react-dom": "^18.2.0", "react-final-form": "^6.5.9", "react-i18next": "^13.0.2", "react-icons": "^4.9.0", "react-idle-timer": "^5.7.2", "react-input-mask": "^2.0.4", "react-router-dom": "^6.13.0", "react-scripts": "5.0.1", "react-tooltip": "^5.16.1", "styled-components": "^6.0.5", "tailwind": "^4.0.0", "typescript": "^4.9.5", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "postbuild": "node iisConfig/copyIISConfig.js", "test": "cypress open", "cy:verify": "cypress verify", "cy:info": "cypress info", "cy:version": "cypress version", "cy:cache:list": "cypress cache list", "cy:run": "cypress run", "cy:run:record": "cypress run --record", "cy:open": "cypress open"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@types/react-input-mask": "^3.0.5", "cypress": "^12.14.0"}}