import { Tooltip } from 'antd';
import React from 'react'
import { useTranslation } from 'react-i18next';
import { RiDeleteBin6Line } from 'react-icons/ri';
import { User } from '../ReportTableManage/index.type';

interface ShareReportsTableColumnInterface{
    setListOfTable: (value: User[]) => void,
    listOfTable: User[],
}
const ShareReportsTableColumn = ({ setListOfTable, listOfTable }: ShareReportsTableColumnInterface) => {
    const { t, i18n } = useTranslation();
    return [
        {
            title: t('reportManagement.userAndGroups.userName'),
            key: 'fullName',
            render: (record: User) => record?.fullName || '',
            sorter: (a: any, b: any) => a.fullName.localeCompare(b?.fullName),
        },
        {
            title: t('reportManagement.userAndGroups.email'),
            key: 'email',
            align: 'center' as 'center',
            render: (record: User) => record?.email || '',
            sorter: (a: any, b: any) => a?.email.localeCompare(b?.email)
        },
        {
            title: t('reportManagement.userAndGroups.actions'),
            key: 'action',
            align: 'center' as 'center',
            render: (record: User) => <div className='flex justify-center'>
                {
                    (<Tooltip placement="left" color='#4d4c4c' title={t('reportManagement.userAndGroups.removeAccess')}>
                        <RiDeleteBin6Line
                            className={`text-[#857f7f] hover:text-red-500 cursor-pointer`}
                            size={15}
                            onClick={() => {
                                const newListOfTable = listOfTable.filter((item: User) => item.userObjectId !== record.userObjectId)
                                setListOfTable(newListOfTable)
                            }}
                        />
                    </Tooltip>)
                }
            </div >
        },
    ]
}

export default ShareReportsTableColumn