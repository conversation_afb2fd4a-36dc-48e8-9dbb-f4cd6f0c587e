import { AxiosError } from "axios";
import { reportDataClient, rethrowError } from "../client/reportDataClient";
import { PBIRole } from "../../Models/pbirole";
import { ReportsMenuItemResource } from "../proxy/TempProxy";
import reportsMenuData from "../../test/data/reportsMenuData";

export const getRoleReportMappingById = (reportId: string, callback: any = null) => {
  try {
    reportDataClient()
      .then(client => new PBIRole(1, "role name", "role desc"))
      // .then(client => client.getRoleReportMappingById(requestId))
      .then((response: PBIRole) => {
        callback && callback(response);
      });
  } catch (error: any) {
    if (error instanceof AxiosError) {
      callback && callback(error);
    } else {
      rethrowError(error);
    }
  }
};

export const getReportsMenu = (callback: any = null): ReportsMenuItemResource[] => {
  return getReportsMenuData();
};

const getReportsMenuData = (): Array<ReportsMenuItemResource> => {
  let data = reportsMenuData();

  let reports = new Array<ReportsMenuItemResource>();
  JSON.parse(data).forEach((report: ReportsMenuItemResource) => {
    reports.push(ReportsMenuItemResource.fromJS(report));
  });

  return reports;
};
