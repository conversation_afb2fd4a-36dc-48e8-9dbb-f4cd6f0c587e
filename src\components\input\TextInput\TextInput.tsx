import { Field } from "react-final-form";
import {
  APPEARANCE,
  INPUT_PADDING,
  ROUNDED_LEFT,
  ROUNDED_RIGHT,
  TEXT_SIZE_WEIGHT
} from "../../../components/commonComponentStyles";
import { FieldProps } from "../../../global";
import { TextInputInterface } from "./TextInput.types";
import Icon from "src/components/Icon/Icon";

const TextInputComponent: React.FC<FieldProps & TextInputInterface> = ({
  input,
  meta,
  appearance,
  icon,
  iconClassName,
  iconSubmit,
  inputClassName = "",
  inputWrapperClassName,
  onClick,
  placeholder,
  rounded,
  size,
  textSize,
  ...props
}: FieldProps & TextInputInterface) => (
  <div
    className={`
      flex w-full
      ${inputWrapperClassName}
    `}
  >
    <input
      {...input}
      className={`
        w-64
        bg-bdo-bg-04-white
        ${APPEARANCE[appearance]}
        ${INPUT_PADDING[size]}
        ${ROUNDED_LEFT[rounded]}
        ${TEXT_SIZE_WEIGHT[textSize]}
        ${inputClassName}
      `}
      inputMode={props.type === "number" ? "numeric" : "text"}
      name={props.name}
      pattern={props.pattern}
      placeholder={props.placeholder}
      type={props.type}
    />
    {icon &&
      <div className={`
        flex px-1 items-center justify-center
       bg-bdo-button-secondary-charcoal-pale
       ${ROUNDED_RIGHT[rounded]}
      `}>
        {iconSubmit &&
          <button
            type="submit"
          >
            <Icon
              className={`
              text-bdo-text-02-white
                ${TEXT_SIZE_WEIGHT[textSize]}
                ${iconClassName}
              `}
              name={icon}
            />
          </button>
        }
        {!iconSubmit &&
          <Icon
            className={`
            text-bdo-text-02-white
              ${TEXT_SIZE_WEIGHT[textSize]}
              ${iconClassName}
            `}
            name={icon}
          />
        }
      </div>
    }
  </div>
);

const TextInput = ({
  appearance = "input",
  icon,
  iconClassName,
  inputClassName = "",
  iconSubmit = false,
  inputWrapperClassName,
  name = "placeholder name",
  onClick,
  pattern,
  placeholder,
  rounded = "md",
  size = "md",
  textSize = "md",
  type = "text",
}: TextInputInterface) => (
  <Field
    appearance={appearance}
    component={TextInputComponent}
    icon={icon}
    iconClassName={iconClassName}
    inputClassName={inputClassName}
    iconSubmit={iconSubmit}
    inputWrapperClassName={inputWrapperClassName}
    onClick={onClick}
    name={name}
    pattern={pattern}
    placeholder={placeholder}
    rounded={rounded}
    size={size}
    textSize={textSize}
    type={type}
  />
);

export default TextInput;
