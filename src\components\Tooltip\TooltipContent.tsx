import { TEXT_SIZE_WEIGHT } from "../commonComponentStyles";
import { TooltipContentInterface } from "./TooltipContent.types";

const TooltipContent = ({
  key,
  label,
  textSize = "default",
  tooltip
}: TooltipContentInterface) => (
  <div key={key}>
    {label &&
      <span
        className={`
          mb-1 text-bdo-text-01-charcoal
          ${TEXT_SIZE_WEIGHT[textSize]}
        `}>
        {`${label}: `}
      </span>
    }
    <span
      className={`
      mb-2 text-sm text-bdo-text-01-charcoal
      ${TEXT_SIZE_WEIGHT[textSize]}
    `}>
      {tooltip}
    </span>
  </div>
);

export default TooltipContent;
