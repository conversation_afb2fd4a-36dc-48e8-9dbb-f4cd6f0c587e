import {BarLoader} from '../Loader';
import { useState} from 'react';
import { Table } from 'antd';
import { BdoTableProps, OnChange, Sorts } from './index.type';

function BdoTable<T>({ columns, context, URL }: BdoTableProps<T>) {
    





    // Order of the columns
    const [sortedInfo, setSortedInfo] = useState<any>({});

    const handleChange: OnChange  = (pagination, filters, sorter) => {
        setSortedInfo(sorter as Sorts);
    };

    // Columns of the table
    const  tableColumns = columns({ sortedInfo });

    return (
        <div>
            <Table
                columns={tableColumns}
                loading={{ indicator: <BarLoader />, spinning: false }}
                // dataSource={dataFiltered}
                onChange={handleChange}
                rowKey={(record) => record.email}
                pagination={{
                    showSizeChanger: true,
                    pageSizeOptions: ['10', '15', '50', '100'],
                    defaultPageSize: 20,
                    // total: 2000
                }}
                scroll={{ y: "calc(100vh - 300px)" }}
            />
            {/* {contextHolder} */}
        </div>
    )
}
export default BdoTable