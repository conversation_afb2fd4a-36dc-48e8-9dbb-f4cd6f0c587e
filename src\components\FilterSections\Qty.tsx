import { useTranslation } from 'react-i18next';
import { qty } from './index.type';

function Qty({ minMaxValue, setMinMaxValue }: qty) {
    const { t } = useTranslation();
    
    const handleChanges = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = { ...minMaxValue, [e.target.name]: e.target.value }
        const re = /^[0-9\b]+$/;
        (e.target.value === '' || re.test(e.target.value)) && setMinMaxValue(newValue)
    }
    return (
        <>
            <h3 className="font-semibold text-[15px]" >{t("userManagement.t17")}</h3>
            <ul className='text-[14px] mt-2 flex justify-start gap-2 qty'>
                <li className="flex gap-2 items-center">
                    <label>{t("userManagement.t18")}</label>
                    <input className='outline-none w-[60px] border border-gray-300 px-2 py-1' maxLength={5} type="text" name="min" value={minMaxValue.min} onChange={handleChanges} />
                </li>
                <li className="flex gap-2 items-center">
                    <label>{t("userManagement.t19")}</label>
                    <input className='outline-none w-[60px] border border-gray-300 px-2 py-1' maxLength={5} type="text" name="max" value={minMaxValue.max} onChange={handleChanges} />
                </li>
            </ul>
        </>
    )
}

export default Qty