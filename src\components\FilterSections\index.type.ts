export type values = {
  [key: string]: boolean
}

export type checkBox = {
  text: string
  checked?: boolean
  values: { name: string; value: boolean }[]
  setValueChecked: (arg: any) => void
}

export type permissions = {
  value: string
  setValue: (arg: any) => void
  OPTIONS: string[]
}

export type status = {
  value: { name: string; id: boolean }[]
  setValue: (arg: any) => void
}

export type qty = {
  minMaxValue: { min: string; max: string }
  setMinMaxValue: (arg: any) => void
}

export interface RadioButtonProps {
  title: string
  options: optionRadioBtnType[]
  radioButtonHandler: (option: any) => void
  value: string
}

export interface DropdownProps {
  title: string
  options: optionRadioBtnType[]
  dropdownHandler: (option: any) => void
  value: optionRadioBtnType
  placeholder?: string
  className?: string
  mandatory?: boolean
}

export type optionRadioBtnType = {
  id: string
  name: string
}
