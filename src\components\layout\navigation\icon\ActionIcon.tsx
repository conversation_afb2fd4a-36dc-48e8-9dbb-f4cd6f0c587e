import { ActionIconInterface } from "./ActionIcon.type";

export const appearanceClasses: any = {
  primary: "text-bdo-text-02-white",
  secondary: "text-bdo-text-01-charcoal",
};

export const appearanceHoverClasses: any = {
  primary: "hover:text-bdo-text-01-charcoal",
  secondaryGray: "hover:text-bdo-text-02-white",
};

export const iconSizes: any = {
  default: "32px",
  lg: "48px",
  md: "32px",
  sm: "24px"
};

const ActionIcon = ({
  appearance = "primary",
  className = "",
  icon: Icon,
  iconSize = "default",
  onClick
}: ActionIconInterface) => (
  <Icon
    className={`
      ${appearanceClasses[appearance]}
      ${appearanceHoverClasses[appearance]}
      ${className}
    `}
    onClick={onClick}
    size={iconSizes[iconSize]}
  />
);

export default ActionIcon;
