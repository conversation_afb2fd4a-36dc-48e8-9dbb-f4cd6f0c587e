import axios from "axios";
import { AxiosError } from "axios";
import { TempProxy } from "../proxy/TempProxy";

const getBearerToken = async () => {
    return "Bearer Token";
};

export const reportDataClient = async () => {
    let hostUrl = process.env.REACT_APP_API_HOSTNAME_LOCAL || "";

    let axiosInstance = axios.create();
    axiosInstance.interceptors.request.use((configuration) => {
        if (configuration && configuration.headers) {
            // configuration.headers["Authorization"] = "Bearer " + getBearerToken();
            configuration.headers["Accept"] = "application/json";
            configuration.headers["Content-Type"] = "application/json";
            configuration.headers["Access-Control-Allow-Origin"] = "*";
            configuration.headers["Access-Control-Allow-Headers"] = "*";
        }
        return configuration;
    });

    return new TempProxy(hostUrl, axiosInstance);
};

export const rethrowError = ((error: any) => {
    throw new AxiosError(error?.cause?.message ?? "Unexpected Client Error");
});
