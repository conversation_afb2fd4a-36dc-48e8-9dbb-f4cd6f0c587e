import "./index.css"
import { CircleLoader } from '../Loader'
import { FaShare, FaUsers, FaUserSlash } from 'react-icons/fa'
import { GlobalContext } from 'src/contextAPI/globalContext'
import { Modal, Table } from 'antd'
import { useNotification } from 'src/hooks/useNotification'
import { User } from '../ReportTableManage/index.type'
import { useTranslation } from 'react-i18next'
import Button from '../input/Button'
import React, { useContext, useState } from 'react'
import ShareReportSearchResults from './ShareReportSearchResults'
import ShareReportsTableColumn from './ShareReportsTableColumn'
import TextInputWithSearchIcon from '../input/TextInputWithSearchIcon'
import useCallApi from 'src/hooks/useCallApi'

function ShareReport() {
    const { t } = useTranslation();
    const { callApiLoading, callApi, isError } = useCallApi()
    const { notification, contextHolder } = useNotification();
    const { oid, isAppAdmin } = useContext(GlobalContext);
    const [searchUserInputValue, setSearchUserInputValue] = useState('')
    const [searchQuery, setSearchQuery] = useState('')
    const [showResults, setShowResults] = useState(false)
    const [modalIsOpen, setModalIsOpen] = useState(false);
    const [listOfTable, setListOfTable] = useState<User[] | []>([])

    const handleSendEmail = async () => { }


    const handleSearch = async (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchUserInputValue(event.target.value)
        setSearchQuery(event.target.value)
        setShowResults(true)
    }

    const handleReset = async () => {
        setSearchUserInputValue('')
        setSearchQuery('')
        setShowResults(false)
    }
    const handleShare = async () => {
        if (listOfTable.length === 0) {
            notification({ content: { name: "Empty list", message: t("reports.share.noUsersSelectedForsharing") }, type: 'error' })
            return
        }

        //*********************************************************************
    }

    return (
        <>
            <Button btnType="main" text="Share" icon={<FaShare />} handleClick={() => setModalIsOpen(!modalIsOpen)} className='outline-none px-[24px]' />
            <Modal
                className='shareReport min-w-[800px] h-[900px] height-modal'
                title={
                    <div className='flex items-center gap-3'>
                        <span className='text-[18px]'>{t("reports.share.shareReport")}</span>
                        <FaUsers size={20} />
                    </div>
                }
                okText={t("reports.share.share")}
                cancelText={t("reports.share.cancel")}
                open={modalIsOpen}
                onOk={handleShare}
                onCancel={() => { handleReset(); setModalIsOpen(false); setListOfTable([]) }}

            >
                <div className='w-full flex flex-col gap-5 '>
                    <div className='relative w-full'>
                        <TextInputWithSearchIcon
                            handleChange={(event) => handleSearch(event)}
                            placeholder={t('reportManagement.userAndGroups.placeholderSearch')}
                            value={searchUserInputValue}
                        />
                        {showResults &&
                            <ShareReportSearchResults
                                searchQuery={searchQuery}
                                setShowResults={setShowResults}
                                setListOfTable={setListOfTable}
                                listOfTable={listOfTable}
                                handleReset={handleReset}
                            />
                        }
                    </div>
                    <h2 className='font-semibold'>{t('reportManagement.userAndGroups.listUserTitle')}</h2>
                    <Table
                        columns={ShareReportsTableColumn({ setListOfTable, listOfTable })}
                        loading={{ indicator: <div><CircleLoader /></div>, spinning: callApiLoading }}
                        dataSource={listOfTable}
                        className='w-full'
                        rowKey={(record: User) => record.email || record.userObjectId}
                        showSorterTooltip={false}
                        pagination={false}
                        scroll={{ y: 350 }}
                        locale={{
                            emptyText() {
                                return <div className='flex justify-center items-center flex-col h-[250px]'><FaUserSlash size={40} /><h2>{t("reports.share.noUsersSelectedForsharing")}</h2></div>
                            },
                        }}
                    />
                </div>
                {contextHolder}
            </Modal>
        </>
    )
}

export default ShareReport