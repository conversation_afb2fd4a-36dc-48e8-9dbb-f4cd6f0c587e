import { useClickAway, useDebounce } from '@uidotdev/usehooks';
import { useNotification } from 'src/hooks/useNotification';
import { UserInterface } from '../UserTableManage/index.type';
import { useTranslation } from 'react-i18next';
import React, { useEffect } from 'react'
import useCall<PERSON>pi from 'src/hooks/useCallApi';
import useUserManagementPage from 'src/hooks/useUserManagementPage';

function UserAddSearchResults({ searchQuery, listOfTable, setListOfTable, checkBoxValue, setShowResults }: any) {
    const { t, i18n } = useTranslation();
    const debouncedSearch = useDebounce(searchQuery, 500);
    const { callApi, callApiLoading, data, setData, isError } = useCallApi();
    const { notification, contextHolder } = useNotification();
    const { fetchUserList } = useUserManagementPage()
    const ref: any = useClickAway(() => setShowResults(false));

    const handleSelect = async (item: UserInterface) => {

        //verify if the user selected is already in the temporary list
        let userExist = listOfTable.find((user: UserInterface) => user.userObjectId === item.userObjectId)
        if (userExist) {
            notification({ content: { name: t("t2"), message: 'This user is already on the temporary list.' }, type: 'error' })
            return
        }

        let extraHeaders = {
            pagesize: 9999,
            pageindex: 1,
            sortBy: "email",
            ascending: true,
            isDeleted: false
        }

        //verify if the user selected is already an app user
        let globalUsers = await fetchUserList({ extraquery: extraHeaders, getTotal: true })
        userExist = globalUsers.find((user: UserInterface) => user.userObjectId === item.userObjectId)
        if (userExist) {
            notification({ content: { name: t("t2"), message: 'This email is already registered as an app user.' }, type: 'error' })
            return
        }

        //structure of the object to be added to the table
        let newUser = {
            userObjectId: item.userObjectId,
            email: item.email,
            fullName: item.fullName,
            isSystemAdmin: checkBoxValue,
            department: item.department || "",
            location: item.location || "",
            businessUnit: item.businessUnit || "",
        }
        //add the user to the table
        setListOfTable([...listOfTable, newUser])
    }

    //Effect to handle search
    useEffect(() => {
        if (debouncedSearch === "") {
            setData([]);
            return;
        }
        callApi({ endpoint: "AssignmentSearchUser", query: debouncedSearch }); //--> API TO CALL TO GET THE LIST OF USERS
    }, [debouncedSearch]);


    //Effect to handle error
    useEffect(() => {
        if (isError.status) {
            notification({ content: { name: t("t2"), message: isError.message }, type: 'error' })
        }
    }, [isError]);

    return (
        <>
            {searchQuery !== "" && debouncedSearch !== "" && (
                <ul ref={ref} className="absolute bg-[#fbfbfb] w-[520px] max-h-96 overflow-y-auto scroll-smooth scroll-thin  z-10 general-shadow m-0 p-0">
                    {
                        callApiLoading
                            ? <li className="w-full py-3 pl-3 pr-2 cursor-pointer text-sm">Loading...</li>
                            : <>
                                {
                                    data.length === 0 && searchQuery !== "" && (
                                        <li className="w-full py-3 pl-3 pr-2 cursor-pointer text-sm">No matching results</li>
                                    )}
                                {
                                    data.map((item: UserInterface) => (
                                        <li
                                            key={item.userObjectId}
                                            className="w-full py-3 pl-3 pr-2 cursor-pointer hover:text-[#ed1a3b] hover:bg-[#fff2ee] text-sm"
                                            onClick={() => handleSelect(item)}>
                                            {item?.email || "No email"}
                                        </li>
                                    ))

                                }
                            </>
                    }
                </ul>
            )}

            {contextHolder}
        </>
    )
}

export default UserAddSearchResults