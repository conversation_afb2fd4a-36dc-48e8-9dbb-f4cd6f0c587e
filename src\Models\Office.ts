export class Office {
  ID: number;
  Office: string;
  wdOfficeGUID: string;

  constructor(ID: number, Office: string, wdOfficeGUID: string) {
    this.ID = ID;
    this.Office = Office;
    this.wdOfficeGUID = wdOfficeGUID;
  }
}
  
export class OfficeList {
  data: Office[];
  message?: any;
  success: boolean;

  constructor(data: Office[], success: boolean, message?: any) {
    this.data = data;
    this.message = message;
    this.success = success;
  }
}
