import { AiOutlineExclamationCircle } from 'react-icons/ai';
import { CategoryManagementPageContext } from 'src/contextAPI/categoryManagementPageContext';
import { GlobalContext } from 'src/contextAPI/globalContext';
import { MdAdd } from 'react-icons/md'
import { Modal } from 'antd'
import { useNotification } from 'src/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import React, { useContext, useState } from 'react'
import TextInputWithSearchIcon from '../input/TextInputWithSearchIcon';
import useCallApi from 'src/hooks/useCallApi';
import useCategoryLogic from 'src/hooks/useCategoryLogic';
import Button from '../input/Button';

const EditModule = () => {
    const { t, i18n } = useTranslation();
    const { callApi, isError, callApiLoading } = useCallApi();
    const { fetchCategoryList } = useCategoryLogic()
    const { categoryList, setIsCollapsible } = useContext(CategoryManagementPageContext)
    const { oid } = useContext(GlobalContext)
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isValidationOn, setIsValidationOn] = useState(false);
    const [isEnglish, setIsEnglish] = useState(i18n.language === "en");
    const { contextHolder, notification } = useNotification();
    const [dataForm, setDataForm] = useState<any>({
        name: { en: "", fr: "" },
        description: { en: "", fr: "" }
    });

    const callApiToAddCategory = async () => {
        const body = { //-> data to send to the server
            id: 0,
            categoryEn: dataForm.name.en.trim(),
            categoryFr: dataForm.name.fr.trim(),
            descriptionEn: dataForm.description.en.trim(),
            descriptionFr: dataForm.description.fr.trim(),
            categoryGroupId: 0,
            submittedByUserId: oid
        }
        let res = await callApi({ endpoint: 'AddCategory', body, method: 'POST' }) //api/category/update
        if (isError.status || res.status !== 200) {
            console.log('error', res)
            notification({ content: { name: t("t2"), message: `${res.status} ${res && res.message || res.statusText || isError && isError.message} ` }, type: 'error' })
            return false
        }
        return true
    }

    const handleOk = async () => {
        setIsValidationOn(true);
        if (!categoryList) return
        if (dataForm.name.en === "" || dataForm.name.fr === "") return

        let isOk = await callApiToAddCategory()
        if (!isOk) return

        fetchCategoryList({ notification })

        //----success, then Refresh the categoryList
        notification({ content: { name: t("success"), message: t("categManagement.addedSuccessfully") }, type: 'success' })
        setIsCollapsible(true)
        setIsModalOpen(false)
    }

    return (
        <>
            <Button
                btnType="main"
                className="px-5 py-3 rounded-[4px] h-[40px]"
                icon={<MdAdd />}
                text={t("categManagement.addCategory")}
                handleClick={() => { setIsModalOpen(!isModalOpen); setIsCollapsible(false); setDataForm({ name: { en: "", fr: "" }, description: { en: "", fr: "" } }) }}
            />
            <Modal
                className='addModule min-w-[700px] h-[700px] height-modal'
                title={t("categManagement.addCategory")}
                okText={t("t7")}
                cancelText={t("t8")}
                open={isModalOpen}
                onOk={handleOk}
                confirmLoading={callApiLoading}
                onCancel={() => { setIsModalOpen(false); setIsCollapsible(true); setDataForm({ name: { en: "", fr: "" }, description: { en: "", fr: "" } }); setIsValidationOn(false) }}
            >
                <hr />
                <form className='flex flex-col justify-start gap-5 mt-5'>
                    <section className='mb-3 flex flex-col w-full gap-3'>
                        <div className="w-full overflow-hidden">

                            {/* Toggle and Information===== */}
                            <section className='flex items-center justify-between my-2 bg-[#ebe8e8c7] px-3 py-3'>
                                <aside className='  flex items-center gap-3'>
                                    <AiOutlineExclamationCircle size={15} color='#e81a3b' />
                                    <span className='text-sm text-[#443b3b]'>{t("categManagement.info")}</span>
                                </aside>
                                <div className="flex justify-end gap-5">
                                    <div className="relative">
                                        <span className={`cursor-pointer ${isEnglish && "font-semibold"} text-[#616161]`} onClick={() => setIsEnglish(true)}>EN</span>
                                        <hr className={`h-1 ${isEnglish && "bg-[#e81a3b]"}`} />
                                        {isValidationOn && dataForm.name.en === "" && <span className="text-red-600 text-2xl absolute -top-0 -left-2">*</span>}
                                    </div>
                                    <div className="relative">
                                        <span className={`cursor-pointer ${!isEnglish && "font-semibold"} text-[#616161]`} onClick={() => setIsEnglish(false)}>FR</span>
                                        <hr className={`h-1 ${!isEnglish && "bg-[#e81a3b]"}`} />
                                        {isValidationOn && dataForm.name.fr === "" && <span className="text-red-600 text-2xl absolute -top-0 -left-2">*</span>}
                                    </div>
                                </div>
                            </section>

                            {/*======== ENGLISH------------------- */}
                            <section className="flex w-[1400px]">
                                <section className={`w-[652px] transition-all ease-in-out duration-300 ${isEnglish ? "ml-0" : "-ml-[652px]"}`}>
                                    <div className="relative">
                                        <div className='flex gap-1'>
                                            <label className='block text-[14px] font-semibold mb-1'>{t("categManagement.englishCategory")}</label>
                                            {dataForm.name.en === "" && <span className="text-red-600 text-2xl">*</span>}
                                        </div>

                                        <TextInputWithSearchIcon
                                            handleChange={(e) => setDataForm({ ...dataForm, name: { en: e.target.value, fr: dataForm.name.fr } })}
                                            placeholder={t("categManagement.placeHolderEnterCategoryEnglishName")}
                                            isToSearch={false}
                                            value={dataForm.name.en}
                                        />
                                        {isValidationOn && dataForm.name.en === "" && <span className="absolute -bottom-4 text-xs text-red-600 right-0">{t("reportManagement.addmodule.fieldRequired")}</span>}
                                    </div>
                                    <div className="mt-3">
                                        <h3 className="font-semibold mb-1 text-[14px]">{t("categManagement.englishDescription")}</h3>
                                        <textarea
                                            className='w-full h-[150px] border border-[#3333331f] rounded-[6px] p-3 resize-none outline-none'
                                            maxLength={500}
                                            onChange={(e) => setDataForm({ ...dataForm, description: { en: e.target.value, fr: dataForm.description.fr } })}
                                            placeholder={t("categManagement.placeHolderEnterCategoryEnglishDescription")}
                                            value={dataForm.description.en}
                                        />
                                        <div className="text-end w-full">
                                            <span className="text-[#7d7b7b] text-xs text-end w-full" >{dataForm.description.en.length}/500</span>
                                        </div>
                                    </div>
                                </section>
                                {/* ====== FRENCH------------------------ */}
                                <section className="w-[652px]">
                                    <div className="relative">
                                        <div className='flex gap-1'>
                                            <label className='block text-[14px] font-semibold mb-1'>{t("categManagement.frenchCategory")}</label>
                                            {dataForm.name.fr === "" && <span className="text-red-600 text-2xl">*</span>}
                                        </div>
                                        <TextInputWithSearchIcon
                                            handleChange={(e) => setDataForm({ ...dataForm, name: { en: dataForm.name.en, fr: e.target.value } })}
                                            placeholder={t("categManagement.placeHolderEnterCategoryFrenchName")}
                                            value={dataForm.name.fr}
                                            isToSearch={false}
                                        />
                                        {isValidationOn && dataForm.name.fr === "" && <span className="absolute -bottom-4 text-xs text-red-600 right-0">{t("reportManagement.addmodule.fieldRequired")}</span>}
                                    </div>
                                    <div className="mt-3">
                                        <h3 className="font-semibold mb-1 text-[14px]">{t("categManagement.frenchDescription")}</h3>
                                        <textarea
                                            className='w-full h-[150px] border border-[#3333331f] rounded-[6px] p-3 resize-none outline-none'
                                            maxLength={500}
                                            onChange={(e) => setDataForm({ ...dataForm, description: { en: dataForm.description.en, fr: e.target.value } })}
                                            placeholder={t("categManagement.placeHolderEnterCategoryFrenchDescription")}
                                            value={dataForm.description.fr}
                                        />
                                        <div className="text-end w-full">
                                            <span className="text-[#7d7b7b] text-xs text-end w-full" >{dataForm.description.fr.length}/500</span>
                                        </div>
                                    </div>
                                </section>
                            </section>
                        </div>
                    </section>
                </form>
                {contextHolder}
            </Modal >
        </>
    )
}

export default EditModule