import { AccordionSearchReportsInterface } from "src/components/layout/navigation/Accordion/AccordionSection.types";
import { LabelResource, ReportsMenuItemResource } from "../../api/proxy/TempProxy";

export interface AllReportsSearchInterface {
  classNameAccordionWrapper?: string
  reportsMenuData: ReportsMenuItemResource[]
  setShowAccordion?: () => void
  showAccordion?: boolean
  isError?: { status: boolean; message: string }
  isLoading?: boolean
  getMenuData: (itemClicked?: AccordionSearchReportsInterface) => void
}
