export class Department {
  departmentID: number;
  businessLineID: number;
  departmentName: string;
  wdDepartmentGUID: string;

  constructor(departmentID: number, departmentName: string, businessLineID: number, wdDepartmentGUID: string) {
    this.businessLineID = businessLineID;
    this.departmentID = departmentID;
    this.departmentName = departmentName;
    this.wdDepartmentGUID = wdDepartmentGUID;
  }
}

export class DepartmentList {
  data: Department[];
  message?: any;
  success: boolean;

  constructor(data: Department[], success: boolean, message?: any) {
    this.data = data;
    this.message = message;
    this.success = success;
  }
}
