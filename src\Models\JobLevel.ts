export class JobLevel {
  id: number;
  jobLevelDescription: string;
  sequence: number;
  statusId: number;
  
  constructor(id: number, jobLevelDescription: string, sequence: number, statusId: number) {
    this.id = id;
    this.jobLevelDescription = jobLevelDescription;
    this.sequence = sequence;
    this.statusId = statusId;
  }
 
}

export class JobLevelList {
  data: JobLevel[];
  message?: any;
  success: boolean;
  
  constructor(data: JobLevel[], success: boolean, message?: any) {
    this.data = data;
    this.message = message;
    this.success = success;
  }
}
