trigger:
  - master

stages:
  - stage: dev
    displayName: Development
    jobs:
      - deployment: dev
        environment: 'DEV'
        pool:
          vmImage: ubuntu-latest
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                
                - download: none  # Asegura un contexto limpio para la descarga
                
                - task: NodeTool@0
                  inputs:
                    versionSpec: '18.x'
                  displayName: 'Install Node.js'
           
                - task: DownloadSecureFile@1
                  name: devEnvFile
                  inputs:
                    secureFile: '.env.development'
                  displayName: 'Download .env.development'

                - task: PowerShell@2
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "##[debug] Contenido del directorio temporal:"
                      Get-ChildItem -Path "$(Agent.TempDirectory)"
                      
                      Write-Host "##[section] Copiando $(devEnvFile.secureFilePath) a .env.production"
                      if (Test-Path -Path "$(devEnvFile.secureFilePath)") {
                        Copy-Item -Path "$(devEnvFile.secureFilePath)" -Destination "$(System.DefaultWorkingDirectory)/.env.production" -Force
                        Write-Host "##[command] Archivo copiado correctamente"
                        Get-Content "$(System.DefaultWorkingDirectory)/.env.production" | ForEach-Object { Write-Host "##[debug] $_" }
                      } else {
                        Write-Host "##[error] Archivo no encontrado: $(devEnvFile.secureFilePath)"
                        exit 1
                      }
                  displayName: 'Copy .env.development to .env.production'

                - script: |
                    npm install
                    npm run build
                  displayName: 'npm install and build'

                - task: ArchiveFiles@2
                  inputs:
                    rootFolderOrFile: 'build'
                    includeRootFolder: false
                    archiveType: 'zip'
                    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
                    replaceExistingArchive: true

                - task: AzureRmWebAppDeployment@4
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: 'BDO PBI DevOps Deployment UAT'
                    appType: 'webApp'
                    WebAppName: 'bdo-ca1-pbi-web-dev-01'
                    packageForLinux: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'

  - stage: qa
    displayName: QA
    jobs:
      - deployment: qa
        environment: 'QA'
        pool:
          vmImage: ubuntu-latest
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                
                - download: none
                
                - task: NodeTool@0
                  inputs:
                    versionSpec: '18.x'
                  displayName: 'Install Node.js'

                - task: DownloadSecureFile@1
                  name: qaEnvFile
                  inputs:
                    secureFile: '.env.qa'
                  displayName: 'Download .env.qa'

                - task: PowerShell@2
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "##[debug] Verificando archivo .env.qa"
                      if (Test-Path -Path "$(qaEnvFile.secureFilePath)") {
                        Copy-Item -Path "$(qaEnvFile.secureFilePath)" -Destination "$(System.DefaultWorkingDirectory)/.env.production" -Force
                        Write-Host "##[command] Archivo .env.qa copiado correctamente"
                      } else {
                        Write-Host "##[error] Archivo .env.qa no encontrado"
                        exit 1
                      }
                  displayName: 'Copy .env.qa to .env.production'

                - script: |
                    npm install
                    npm run build
                  displayName: 'npm install and build'

                - task: ArchiveFiles@2
                  inputs:
                    rootFolderOrFile: 'build'
                    includeRootFolder: false
                    archiveType: 'zip'
                    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
                    replaceExistingArchive: true

                - task: AzureRmWebAppDeployment@4
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: 'BDO PBI DevOps Deployment UAT'
                    appType: 'webApp'
                    WebAppName: 'bdo-ca1-pbi-web-qa-01'
                    packageForLinux: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'

  - stage: uat
    displayName: UAT
    jobs:
      - deployment: uat
        environment: 'UAT'
        pool:
          vmImage: ubuntu-latest
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                
                - download: none
                
                - task: NodeTool@0
                  inputs:
                    versionSpec: '18.x'
                  displayName: 'Install Node.js'

                - task: DownloadSecureFile@1
                  name: uatEnvFile
                  inputs:
                    secureFile: '.env.uat'
                  displayName: 'Download .env.uat'

                - task: PowerShell@2
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "##[debug] Verificando archivo .env.uat"
                      if (Test-Path -Path "$(uatEnvFile.secureFilePath)") {
                        Copy-Item -Path "$(uatEnvFile.secureFilePath)" -Destination "$(System.DefaultWorkingDirectory)/.env.production" -Force
                        Write-Host "##[command] Archivo .env.uat copiado correctamente"
                      } else {
                        Write-Host "##[error] Archivo .env.uat no encontrado"
                        exit 1
                      }
                  displayName: 'Copy .env.uat to .env.production'

                - script: |
                    npm install
                    npm run build
                  displayName: 'npm install and build'

                - task: ArchiveFiles@2
                  inputs:
                    rootFolderOrFile: 'build'
                    includeRootFolder: false
                    archiveType: 'zip'
                    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
                    replaceExistingArchive: true

                - task: AzureRmWebAppDeployment@4
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: 'BDO PBI DevOps Deployment - PreProd'
                    appType: 'webApp'
                    WebAppName: 'bdo-ca1-pbi-web-uat2-01'
                    packageForLinux: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'

  - stage: prod
    displayName: Production
    jobs:
      - deployment: prod
        environment: 'prod'
        pool:
          vmImage: ubuntu-latest
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                
                - download: none
                
                - task: NodeTool@0
                  inputs:
                    versionSpec: '18.x'
                  displayName: 'Install Node.js'

                - task: DownloadSecureFile@1
                  name: prodEnvFile
                  inputs:
                    secureFile: '.env.production'
                  displayName: 'Download .env.production'

                - task: PowerShell@2
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "##[debug] Verificando archivo .env.production"
                      if (Test-Path -Path "$(prodEnvFile.secureFilePath)") {
                        Copy-Item -Path "$(prodEnvFile.secureFilePath)" -Destination "$(System.DefaultWorkingDirectory)/.env.production" -Force
                        Write-Host "##[command] Archivo .env.production copiado correctamente"
                      } else {
                        Write-Host "##[error] Archivo .env.production no encontrado"
                        exit 1
                      }
                  displayName: 'Copy .env.production'

                - script: |
                    npm install
                    npm run build
                  displayName: 'npm install and build'

                - task: ArchiveFiles@2
                  inputs:
                    rootFolderOrFile: 'build'
                    includeRootFolder: false
                    archiveType: 'zip'
                    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
                    replaceExistingArchive: true

                - task: AzureRmWebAppDeployment@4
                  inputs:
                    ConnectionType: 'AzureRM'
                    azureSubscription: 'BDO PBI DevOps Deployment PROD'
                    appType: 'webApp'
                    WebAppName: 'bdo-ca1-pbi-web-prod-01'
                    packageForLinux: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'