import "./i18n";
import "./index.css";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { GlobalContextProvider } from "./contextAPI/globalContext";
import { InteractionType, PublicClientApplication } from "@azure/msal-browser";
import { MsalAuthenticationResult, MsalAuthenticationTemplate, MsalProvider } from "@azure/msal-react";
import App from "./App";
import msalConfig from "./auth/configuration/authConfig";
import React, { Fragment } from "react";
import ReactDOM from "react-dom/client";
import reportWebVitals from "./reportWebVitals";

const msalAuthenticationInsance = new PublicClientApplication(msalConfig);

const ErrorComponent: React.FC<MsalAuthenticationResult> = ({ error }) => {
  return (
    <Fragment>
      <div className="h4">
        An Error Occurred: {error && error.errorCode ? error.errorCode : "an unknown authorization error occurred"}
      </div>
    </Fragment>
  );
};

const Loading = () => {
  return (
    <Fragment>
      <div className="h4">Authentication in progress...</div>
    </Fragment>
  );
};

const root = ReactDOM.createRoot(document.getElementById("root") as HTMLElement);
root.render(
  <BrowserRouter>
    <MsalProvider instance={msalAuthenticationInsance}>
      <MsalAuthenticationTemplate
        interactionType={InteractionType.Redirect}
        errorComponent={ErrorComponent}
        loadingComponent={Loading}
      >
        <GlobalContextProvider>
          <App />
        </GlobalContextProvider>
      </MsalAuthenticationTemplate>
    </MsalProvider>
  </BrowserRouter>
);
reportWebVitals();
