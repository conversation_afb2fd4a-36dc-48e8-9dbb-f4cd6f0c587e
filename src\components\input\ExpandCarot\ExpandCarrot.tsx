import { ExpandCarrotInterface } from "./ExpandCarrot.types";

const ExpandCarrot = ({ expanded }: ExpandCarrotInterface) => {
  return (
    <>
      {expanded && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="icon icon-tabler icon-tabler-chevron-left ml-auto mr-[3px] cursor-pointer"
          width="22"
          height="22"
          viewBox="0 0 24 24"
          strokeWidth="2"
          stroke="#ea2948"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <path d="M15 6l-6 6l6 6" />
        </svg>
      )}
    </>
  );
};

export default ExpandCarrot;
