import { FaUserAlt, FaUsers } from 'react-icons/fa'
import { GlobalContext } from 'src/contextAPI/globalContext';
import { handleSearchUserAndGroupType, responseFetchAccessList, UserAndGroupAssignmentType } from './index.type';
import { useTranslation } from 'react-i18next';
import Modal from 'antd/es/modal/Modal'
import React, { useContext, useEffect, useState } from 'react'
import useCallApi from 'src/hooks/useCallApi';
import UserAndGroupAssignmentTable from './UserAndGroupAssignmentTable';

const UserAndGroupAssignment = ({ report }: UserAndGroupAssignmentType) => {
  const { t, i18n } = useTranslation();
  const { oid, isAppAdmin } = useContext(GlobalContext);
  const [searchUserInputValue, setSearchUserInputValue] = useState({ user: '', group: '' })
  const { callApiLoading, callApi, isError } = useCallApi()
  const [listOfTable, setListOfTable] = useState<responseFetchAccessList>({ user: [], group: [] })
  const [masterCopyListOfTable, setMasterCopyListOfTable] = useState<responseFetchAccessList>({ user: [], group: [] })
  const [modalInfo, setModalInfo] = useState({
    isOpen: false,
    title: { id: 1, name: t('reportManagement.userAndGroups.userTitle') }
  });


  useEffect(() => {
    setModalInfo({ ...modalInfo, title: { id: 1, name: t('reportManagement.userAndGroups.userTitle') } })
  }, [i18n.language])


  //To populate the table with the list of users and groups in the first render
  const fetchData = async () => {
    setModalInfo({ ...modalInfo, isOpen: true })
    let extraHeaders = {
      userObjectId: oid,
      reportId: report.id,
      accessType: 1
    }
    let user_response = await callApi({ endpoint: 'AssignmentList', extraHeaders })
    let group_response = await callApi({ endpoint: 'AssignmentList', extraHeaders: { ...extraHeaders, accessType: 2 } })
    setMasterCopyListOfTable({ user: user_response, group: group_response })
    setListOfTable({ user: user_response, group: group_response })
  }

  const handleReset = () => {
    setModalInfo({ isOpen: false, title: { id: 1, name: t('reportManagement.userAndGroups.userTitle') } })
    setSearchUserInputValue({ user: '', group: '' })
  }

  //Mehod to handle the search Input of users and groups
  const handleSearch = async ({ event, assignmentType }: handleSearchUserAndGroupType) => {

    const normalizedEventValue = event.target.value.trim().toLowerCase();

    //1 for user and 2 for group
    if (assignmentType === 1) {

      setSearchUserInputValue({ ...searchUserInputValue, user: normalizedEventValue })

      if (normalizedEventValue.length === 0) {
        setListOfTable({ ...listOfTable, user: masterCopyListOfTable.user })
        return
      }
      // Filter users based on the search input
      const filteredUsers = masterCopyListOfTable.user.filter((user) => {
        return (
          user.user?.fullName.toLowerCase().includes(normalizedEventValue));
      });
      // Update the list of users in the table
      setListOfTable({ ...listOfTable, user: filteredUsers })
    }
    else {
      setSearchUserInputValue({ ...searchUserInputValue, group: normalizedEventValue })
      if (normalizedEventValue.length === 0) {
        setListOfTable({ ...listOfTable, group: masterCopyListOfTable.group })
        return
      }
      // Filter groups based on the search input
      const filteredGroups = masterCopyListOfTable.group.filter((group) => {
        return (
          group?.groupName?.toLowerCase().includes(normalizedEventValue));
      });
      // Update the list of groups in the table
      setListOfTable({ ...listOfTable, group: filteredGroups })
    }
  }

  return (
    <>
      <FaUsers size={20} onClick={async () => await fetchData()} />
      <Modal
        footer={null}
        className='group-assignment userandgroup-assignment min-w-[900px] h-[900px] height-modal'
        title={
          <div className='flex items-center gap-3'>
            <span className='text-[18px]'>{modalInfo.title.name}</span>
            {modalInfo.title.id === 1 ? <FaUserAlt size={15} /> : <FaUsers size={20} />}
          </div>
        }
        open={modalInfo.isOpen}
        onCancel={() => handleReset()}
      >
        <aside className='flex gap-4 absolute top-0 right-0 text-[15px] py-4 pl-[22px] pr-16 rounded-bl-md bg-[#dddddd7a]'>
          <div>{t("reportManagement.userAndGroups.assignmentBy")}:</div>
          <div className='relative'>
            <h2
              className={`${modalInfo.title.id === 1 && 'font-semibold'} cursor-pointer hover:text-red-600 `}
              onClick={() => setModalInfo({ ...modalInfo, title: { id: 1, name: t('reportManagement.userAndGroups.userTitle') } })}
            >
              {t('reportManagement.userAndGroups.user')}
            </h2>
            <hr className={`${modalInfo.title.id === 1 && 'bg-[#e81a3b]'} border-0 h-1`} />
          </div>
          {/* ---- Groups Table ---- */}
          {
            isAppAdmin &&
            <div className='relative'>
              <h2
                className={`${modalInfo.title.id === 2 && 'font-semibold'} cursor-pointer hover:text-red-600`}
                onClick={() => setModalInfo({ ...modalInfo, title: { id: 2, name: t('reportManagement.userAndGroups.groupTitle') } })}
              >
                {t('reportManagement.userAndGroups.group')}
              </h2>
              <hr className={`${modalInfo.title.id === 2 && 'bg-[#e81a3b]'} border-0 h-1`} />
            </div>
          }
        </aside>
        <h2 className='text-[#e81a3b] py-3'>{i18n.language === "fr" ? report.nameFr : report.nameEn}</h2>
        <UserAndGroupAssignmentTable
          modalInfo={modalInfo}
          listOfTable={listOfTable}
          setListOfTable={setListOfTable}
          setMasterCopyListOfTable={setMasterCopyListOfTable}
          masterCopyListOfTable={masterCopyListOfTable}
          searchUserInputValue={searchUserInputValue}
          setSearchUserInputValue={setSearchUserInputValue}
          callApiLoading={callApiLoading}
          handleSearch={handleSearch}
          report={report}
        />
      </Modal>
    </>
  )
}

export default UserAndGroupAssignment