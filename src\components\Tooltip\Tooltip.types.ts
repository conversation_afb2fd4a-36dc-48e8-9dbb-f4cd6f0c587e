export interface TooltipInterface {
  items: any[] | undefined;
  dataTooltipId: string;
  place?: TooltipPlace;
  rounded?: TooltipRounded;
  size?: TooltipSize;
  textSize?: TooltipTextSize;
}

export type TooltipPlace = "top" | "top-start" | "top-end"
  | "right" | "right-start" | "right-end"
  | "bottom" | "bottom-start" | "bottom-end"
  | "left" | "left-start" | "left-end";

export type TooltipRounded = "default" | "lg" | "md" | "sm";

export type TooltipSize = "default" | "lg" | "md" | "sm";

export type TooltipTextSize = "default" | "sm";
