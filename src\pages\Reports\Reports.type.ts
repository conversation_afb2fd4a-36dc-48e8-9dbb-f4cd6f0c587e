export interface ApiStructure {
  userAuth: UserAuth
  categories: Category[]
}

export interface Category {
  categoryEn: string
  categoryFr: string
  descriptionEn: null
  descriptionFr: null
  displayOrder: number
  categoryGroup: CategoryGroup
  reports: Report[]
}

export interface CategoryGroup {
  id: number
  nameEn: string
  nameFr: string
}

export interface Report {
  id: string
  powerBIReportId: string
  workSpaceID: string
  nameEn: string
  nameFr: string
  descriptionEn: string
  descriptionFr: null
  isPublic: boolean
  isConfidential: boolean
  authMethod: string
  isReportAdmin: boolean
  isFavorite?: boolean
}

export interface UserAuth {
  name: string
  roles: string[]
}
