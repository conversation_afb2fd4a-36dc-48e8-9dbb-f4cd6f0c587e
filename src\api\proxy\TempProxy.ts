import axios from 'axios';
import { AxiosInstance } from "axios";

export class TempProxy {
  private instance: AxiosInstance;
  private baseUrl: string;

  constructor(baseUrl?: string, instance?: AxiosInstance) {
    this.instance = instance ? instance : axios.create();
    this.baseUrl = baseUrl !== undefined && baseUrl !== null ? baseUrl : "http://localhost:8080";
  }
}

export interface IAuthResource {
  name?: string;
  roles?: string[];
  [key: string]: any;
}

export class AuthResource implements IAuthResource {
  name?: string;
  roles?: string[];

  [key: string]: any;

  constructor(data?: IAuthResource) {
    if (data) {
      for (var property in data) {
        if (data.hasOwnProperty(property))
          (<any>this)[property] = (<any>data)[property];
        }
    }
  }

  init(_data?: any) {
    if (_data) {
      for (var property in _data) {
        if (_data.hasOwnProperty(property))
          this[property] = _data[property];
      }
      this.name = _data["name"];
      if (Array.isArray(_data["roles"])) {
        this.roles = [] as any;
        for (let item of _data["roles"])
          this.roles!.push(item);
      }
    }
  }

  static fromJS(data: any): AuthResource {
    data = typeof data === 'object' ? data : {};
    let result = new AuthResource();
    result.init(data);
    return result;
  }

  toJSON(data?: any) {
    data = typeof data === 'object' ? data : {};
    for (var property in this) {
      if (this.hasOwnProperty(property))
        data[property] = this[property];
    }
    data["en"] = this.en;
    if (Array.isArray(this.roles)) {
      data["roles"] = [];
      for (let item of this.roles)
        data["roles"].push(item);
    }
    return data;
  }
}

export interface ILabelResource {
  en?: string;
  fr?: string;
  [key: string]: any;
}

export class LabelResource implements ILabelResource {
  en?: string;
  fr?: string;

  [key: string]: any;

  constructor(data?: ILabelResource) {
    if (data) {
      for (var property in data) {
        if (data.hasOwnProperty(property))
          (<any>this)[property] = (<any>data)[property];
      }
    }
  }

  init(_data?: any) {
    if (_data) {
      for (var property in _data) {
        if (_data.hasOwnProperty(property))
          this[property] = _data[property];
      }
      this.en = _data["en"];
      this.fr = _data["fr"];
    }
  }

  static fromJS(data: any): LabelResource {
    data = typeof data === 'object' ? data : {};
    let result = new LabelResource();
    result.init(data);
    return result;
  }

  toJSON(data?: any) {
    data = typeof data === 'object' ? data : {};
    for (var property in this) {
      if (this.hasOwnProperty(property))
        data[property] = this[property];
    }
    data["en"] = this.en;
    data["fr"] = this.fr;
    return data;
  }
}

export interface IReportMenuTargetResource {
  reportName?: LabelResource;
  reportGuid?: string
  reportId?: string
  workspaceGuid?: string;
  [key: string]: any;
}

export class ReportMenuTargetResource implements IReportMenuTargetResource {
  reportName?: LabelResource;
  reportGuid?: string
  reportId?: string
  workspaceGuid?: string;
  isFavorite?: boolean;

  [key: string]: any;

  constructor(data?: IReportMenuTargetResource) {
    if (data) {
      for (var property in data) {
        if (data.hasOwnProperty(property))
          (<any>this)[property] = (<any>data)[property];
      }
    }
  }

  init(_data?: any) {
    if (_data) {
      for (var property in _data) {
        if (_data.hasOwnProperty(property))
          this[property] = _data[property];
      }
      this.reportName = _data["reportName"] ? LabelResource.fromJS(_data["reportName"]) : <any> undefined;
      this.reportGuid = _data["reportGuid"];
      this.reportId = _data["reportId"];
      this.workspaceGuid = _data["workspaceGuid"];
    }
  }

  static fromJS(data: any): LabelResource {
    data = typeof data === 'object' ? data : {};
    let result = new LabelResource();
    result.init(data);
    return result;
  }

  toJSON(data?: any) {
    data = typeof data === 'object' ? data : {};
    for (var property in this) {
      if (this.hasOwnProperty(property))
        data[property] = this[property];
    }
    data["reportName"] = this.reportName ? this.reportName.toJSON() : <any> undefined;
    data["reportGuid"] = this.reportGuid;
    data["reportId"] = this.reportId;
    data["workspaceGuid"] = this.workspaceGuid;
    return data;
  }
}

export interface IReportsMenuItemResource {
  auth?: AuthResource
  category?: LabelResource;
  label?: LabelResource;
  target?: ReportMenuTargetResource;
  children?: ReportsMenuItemResource[];
}

export class ReportsMenuItemResource implements IReportsMenuItemResource {
  auth?: AuthResource;
  category?: LabelResource;
  label?: LabelResource;
  target?: ReportMenuTargetResource;
  children?: ReportsMenuItemResource[];

  [key: string]: any;

  constructor(data?: ReportsMenuItemResource) {
    if (data) {
      for (var property in data) {
        if (data.hasOwnProperty(property))
          (<any>this)[property] = (<any>data)[property];
      }
    }
  }

  init(_data?: any) {
    if (_data) {
      for (var property in _data) {
        if (_data.hasOwnProperty(property))
          this[property] = _data[property];
      }
      this.auth = _data["auth"] ? AuthResource.fromJS(_data["auth"]) : <any> undefined;
      this.category = _data["category"] ? LabelResource.fromJS(_data["category"]) : <any> undefined;
      this.label = _data["label"] ? LabelResource.fromJS(_data["label"]) : <any> undefined;
      this.target = _data["target"] ? ReportMenuTargetResource.fromJS(_data["target"]) : <any> undefined;
      if (Array.isArray(_data["children"])) {
          this.children = [] as any;
          for (let item of _data["children"])
              this.children!.push(ReportsMenuItemResource.fromJS(item));
      }
    }
  }

  static fromJS(data: any): ReportsMenuItemResource {
    data = typeof data === 'object' ? data : {};
    let result = new ReportsMenuItemResource();
    result.init(data);
    return result;
  }

  toJSON(data?: any) {
    data = typeof data === 'object' ? data : {};
    for (var property in this) {
      if (this.hasOwnProperty(property))
        data[property] = this[property];
    }
    data["auth"] = this.auth ? this.auth.toJSON() : <any> undefined;
    data["category"] = this.category ? this.category.toJSON() : <any> undefined;
    data["label"] = this.label ? this.label.toJSON() : <any> undefined;
    data["target"] = this.target ? this.target.toJSON() : <any> undefined;
    if (Array.isArray(this.children)) {
      data["children"] = [];
      for (let item of this.children)
        data["children"].push(item.toJSON());
    }
    return data;
  }
}