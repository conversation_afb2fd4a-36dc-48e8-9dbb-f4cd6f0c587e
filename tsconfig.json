{
  "compilerOptions": {
    "baseUrl": "./",
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "ES2022"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "ES2020",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
  },
  "include": ["global.d.ts", "**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules"]
}
