
type Props = {
    text: string,
    name: string,
    setSelection: (arg: any) => void;
    selected: string;
}
function Radio({ text, name, setSelection, selected }: Props) {
    return (
        <>
            <input
                name={name}
                type='radio'
                id={text}
                className="cursor-pointer  accent-[#e04b4b]"
                onChange={(e) => setSelection(e.target.id)}
                checked={selected === text}
            />
            <label htmlFor={text}>{text}</label>
        </>
    )
}
export default Radio