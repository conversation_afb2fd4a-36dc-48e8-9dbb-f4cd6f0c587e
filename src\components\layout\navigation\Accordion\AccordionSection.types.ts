import { ReactNode } from 'react'
import { ComponentAppearance } from '../../../componentProperties/componentProperties.types'
import { MenuItemInterface, AccordionInterface } from './Accordion.types'
import { LabelResource } from 'src/api/proxy/TempProxy'
import { useCallApiType } from 'src/hooks/hooks.types'

export interface AccordionSectionInterface {
  appearance?: ComponentAppearance
  children: ReactNode
  isOpen: boolean
  item?: MenuItemInterface
  onClick: () => void
  onClickCollapse: () => void
  selected?: boolean

}

export interface AccordionSearchReportsInterface {
  id: string
  powerBIReportId: string
  workSpaceID: string
  nameEn: string
  nameFr: string
  descriptionEn: string | null
  descriptionFr: string | null
  category: string | null
  categoryId: number
  authMethod: number
  externalAccessible: boolean
  isPrivate: boolean
  isActive: boolean
  displayOrder: number
  dateAdded: string
  reportAccess: any | null
}

export type propsAccordionSearch = {
  searchQuery: string
  getMenuData: (itemClicked?: AccordionSearchReportsInterface) => void
  setShowResults: (show: boolean) => void
  urlToCall: useCallApiType['endpoint']
}
