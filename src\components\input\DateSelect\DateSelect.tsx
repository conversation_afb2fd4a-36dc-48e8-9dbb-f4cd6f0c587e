import { useState } from "react";
import DateSelectInterface from "./DateSelect.types";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Icon from "../../../components/Icon/Icon";
import {
  APPEARANCE,
  INPUT_PADDING,
  ROUNDED,
  TEXT_SIZE_WEIGHT
} from "../../../components/commonComponentStyles";

const DateSelect = ({
  appearance = "primary",
  className = "",
  handleDateSelect,
  isRequired,
  label,
  name = "",
  placeholder,
  rounded = "default",
  selectedDate,
  size = "default",
  textSize = "sm",
  timeInterval = 60,
  type = "date"
}: DateSelectInterface) => {
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  return (
    <label className="w-fit">
      {label && (
        <div className="mb-1 text-sm text-bdo-text-01-charcoal">
          {label}
          {isRequired && <span className="pl-1 text-bdo-button-primary-red">*</span>}
        </div>
      )}
      <div className={`
          flex items-center w-48
          ${APPEARANCE[appearance]}
          ${ROUNDED[rounded]}
          ${className}
        `}
      >
        <DatePicker
          className={`
            flex w-32
            ${TEXT_SIZE_WEIGHT[textSize]}
            ${INPUT_PADDING[size]}
          `}
          dateFormat={type === "date" ? "yyyy-MM-dd" : "HH:mm"}
          name={name}
          open={isDatePickerOpen}
          onChange={(date: Date) => { handleDateSelect && handleDateSelect(date); }}
          onClickOutside={() => { setIsDatePickerOpen(!isDatePickerOpen); }}
          placeholderText={
            placeholder ??
              type === "date"
                ? "yyyy-MM-dd" 
                : "00:00"
          }
          selected={selectedDate}
          showTimeSelect={type === "time"}
          showTimeSelectOnly={type === "time"}
          timeCaption={type === "time" ? "Time" : undefined}
          timeIntervals={type === "time" ? timeInterval : undefined}
        />
        <button
          className="flex ml-auto pr-2"
          onClick={() => { setIsDatePickerOpen(!isDatePickerOpen); }}
          tabIndex={0}
          type="button"
          >
            <Icon name="Calendar_Month" className="text-bdo-text-01-charcoal" />
        </button>
      </div>
    </label>
  );
};

export default DateSelect;
