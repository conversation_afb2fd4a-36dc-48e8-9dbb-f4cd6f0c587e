import { NavIconInterface } from "./NavIcon.type";

export const appearanceClasses: any = {
  default: "text-bdo-text-02-white",
  primary: "text-bdo-text-02-white",
  secondary: "text-bdo-text-01-charcoal",
};

export const appearanceHoverClasses: any = {
  default: "hover:text-bdo-button-secondary-charcoal-pale",
  primary: "hover:text-bdo-button-secondary-charcoal-pale",
  secondaryGray: "hover:text-bdo-text-02-white",
};

export const iconSizes: any = {
  default: "32px",
  lg: "48px",
  md: "32px",
  sm: "24px",
};

const NavIcon = ({
  appearance = "primary",
  className = "",
  icon: Icon,
  iconSize = "default",
  navigate
}: NavIconInterface) => (
  <Icon
    className={`
      ${appearanceClasses[appearance]}
      ${appearanceHoverClasses[appearance]}
      ${className}
    `}
    onClick={navigate}
    size={iconSizes[iconSize]}
  />
);

export default NavIcon;
