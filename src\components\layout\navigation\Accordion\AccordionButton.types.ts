import { ReactNode } from "react";
import {
  ComponentAppearance,
  ComponentSize,
  ComponentTextSize
} from "../../../componentProperties/componentProperties.types";

export interface AccordionButtonInterface {
  appearance?: ComponentAppearance;
  className?: string;
  children?: ReactNode;
  onClick: (value: string) => void;
  selected?: boolean;
  size?: ComponentSize;
  textSize?: ComponentTextSize;
  value: string;
  qtty?: number;
}
