export class RoleCriteria {
  //id: number;
  operator: string;
  type: string;
  value: string;

  constructor(type: string, value: string, operator: string) {
    this.type = type;
    this.value = value;
    this.operator = operator;
  }
}

export class RoleCriteriaWithDesc {
  //id: number;
  desc: string;
  operator: string;
  type: string;
  value: string;

  constructor(type: string, value: string, operator: string, desc: string) {
    this.desc = desc;
    this.operator = operator;
    this.type = type;
    this.value = value;
  }
}

export class RoleCriteriaMappingsWithDesc {
  roleId: number;
  description?: any;
  roleName: string;
  criterias: RoleCriteriaWithDesc[];

  constructor(roleId: number, roleName: string, criterias: RoleCriteriaWithDesc[], description?: any) {
    this.criterias = criterias;
    this.description = description;
    this.roleId = roleId;
    this.roleName = roleName;
  }
}

export class RoleCriteriaMappings {
  roleId: number;
  description?: any;
  roleName: string;
  criterias: RoleCriteria[];

  constructor(roleId: number, roleName: string, criterias: RoleCriteriaWithDesc[], description?: any) {
    this.criterias = criterias;
    this.description = description;
    this.roleId = roleId;
    this.roleName = roleName;
  }
}

export class RoleCriteriaMapping {
  roleId: number;
  //roleName: string;
  //description?: any;
  //criteriaId?: number;
  criteria: RoleCriteria;

  constructor(roleId: number, criteria: RoleCriteria) {
    this.criteria = criteria;
    this.roleId = roleId;
  }
}

export class RoleCriteriaMappingDto {
  RoleId: number;
  CriteriaId: number;
  Criteria: RoleCriteria;

  constructor(roleId: number, CriteriaId: number, criteria: RoleCriteria) {
    this.RoleId = roleId;
    this.Criteria = criteria;
    this.CriteriaId = CriteriaId;
  }
}

export class CriteriaMapping {
  roleId: number;
  CriteriaId: number;

  constructor(roleId: number, CriteriaId: number) {
    this.CriteriaId = CriteriaId;
    this.roleId = roleId;
  }
}
