import { checkBox } from "src/components/FilterSections/index.type";
import "./style.css"

function Checkbox({ text, setValueChecked, checked, values }: checkBox) {

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target
        const newValues = values.map((item: { name: string; }) => {
            if (item.name === name)
                return { name, value: checked }
            return item
        })
        setValueChecked(newValues)
    }
    return (
        <div className="flex items-center gap-3">
            <input
                className="cursor-pointer checkbox"
                name={text}
                type='checkbox'
                onChange={(e) => handleChange(e)}
                checked={checked}
            />
            <label>{text}</label>
        </div>
    )
}
export default Checkbox